<template>
	<view class="edit-container">
		<!-- Canvas画布区域 -->
		<view class="canvas-container" id="canvasContainer" 
			@touchstart="onContainerTouchStart"
			@touchmove="onContainerTouchMove"
			@touchend="onContainerTouchEnd">
			<canvas 
				canvas-id="editCanvas" 
				id="editCanvas"
				class="edit-canvas" 
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
			></canvas>
		</view>
		
		<!-- 分隔线 -->
		<view class="divider"></view>
		
		<!-- 属性设置界面区域 -->
		<scroll-view scroll-y class="property-section">
			<!-- 模版名称区域 -->
			<view class="template-name-section">
				<input 
					v-model="templateName" 
					class="template-name-input" 
					placeholder="未命名"
				/>
			</view>
			<!-- 模版类型 -->
			<view class="template-type-section">
				<text class="template-type-label">模板类型</text>
				<picker 
					:value="templateTypeIndex" 
					:range="templateTypeList" 
					@change="onTemplateTypeChange"
					class="template-type-picker"
				>
					<view class="picker-view">
						<text class="picker-text">{{templateTypeList[templateTypeIndex] || '请选择'}}</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<!-- 对象插入和背景选择区域 -->
			<view class="bottom-controls">
				<!-- 对象插入 -->
				<view class="control-group">
					<text class="group-title">对象插入</text>
					<view class="button-row">
						<view class="control-btn" @click="insertTemplateText">模版文字</view>
						<view class="control-btn" @click="insertFixedText">固定文字</view>
						<view class="control-btn" @click="insertImage">图片</view>
					</view>
				</view>
				
				<!-- 背景选择 -->
				<view class="control-group">
					<text class="group-title">背景选择</text>
					<view class="button-row">
						<view class="control-btn" @click="selectBackground">选择背景</view>
						<view class="control-btn" @click="setSolidBackground">纯色背景</view>
						<view class="control-btn" @click="clearBackground">清除背景</view>
					</view>
				</view>
				
				<!-- 批量操作 -->
				<view class="control-group">
					<text class="group-title">批量操作</text>
					<view class="button-row">
						<view class="control-btn" @click="centerAllElements">水平居中</view>
						<view class="control-btn" @click="autoAdjustSpacing">自适应间距</view>
						<view class="control-btn special-btn" @click="beautifyAllElements">一键美化</view>
					</view>
				</view>
			</view>
			<!-- 属性设置标题 -->
			<view class="property-title">
				<text>属性设置</text>
				<text v-if="!selectedElement" class="property-subtitle">（修改所有文本元素）</text>
				<text v-else class="property-subtitle">（编辑当前元素）</text>
			</view>
			
			<!-- 批量操作选项 -->
			<view v-if="!selectedElement" class="batch-options" style="display: none;">
				<view class="batch-option" @click="showBatchOptions">
					<text class="batch-option-text">批量操作选项</text>
					<text class="batch-option-arrow">▼</text>
				</view>
			</view>
			
			<!-- 文字相关属性 -->
			<view class="property-group">
				<!-- 文字字体 -->
				<view class="property-item">
					<text class="property-label">文字字体</text>
					<picker 
						:value="fontFamilyIndex" 
						:range="fontFamilyList" 
						@change="onFontFamilyChange"
						class="property-picker"
					>
						<view class="picker-view">
							<text class="picker-text">{{fontFamilyList[fontFamilyIndex] || '请选择'}}</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
				
				<!-- 文字颜色 -->
				<view class="property-item">
					<text class="property-label">文字颜色</text>
					<view class="color-picker" @click="showColorPicker = 'text'">
						<view class="color-preview" :style="{backgroundColor: textColor}"></view>
						<text class="color-name">{{colorNameMap[textColor]}}</text>
						<text class="picker-arrow">▼</text>
					</view>
				</view>
				
				<!-- 内部对齐 -->
			<!-- 	<view class="property-item">
					<text class="property-label">内部对齐</text>
					<picker 
						:value="textAlignIndex" 
						:range="textAlignList" 
						range-key="name"
						@change="onTextAlignChange"
						class="property-picker"
					>
						<view class="picker-view">
							<text class="picker-text">{{(textAlignList[textAlignIndex] && textAlignList[textAlignIndex].name) || '居中'}}</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
				 -->
				<!-- 水平位置 -->
				<!-- <view class="property-item">
					<text class="property-label">水平位置</text>
					<view class="position-controls">
						<view 
							v-for="(item, index) in horizontalAlignIcons" 
							:key="index"
							class="position-icon" 
							:class="{active: horizontalAlign === item.value}"
							@click="setHorizontalAlign(item.value)"
						>
							<text class="align-icon">{{item.icon}}</text>
						</view>
					</view>
				</view>
				 -->
				<!-- 垂直位置 -->
				<!-- <view class="property-item">
					<text class="property-label">垂直位置</text>
					<view class="position-controls">
						<view 
							v-for="(item, index) in verticalAlignIcons" 
							:key="index"
							class="position-icon" 
							:class="{active: verticalAlign === item.value}"
							@click="setVerticalAlign(item.value)"
						>
							<text class="align-icon">{{item.icon}}</text>
						</view>
					</view>
				</view>
				 -->
				<!-- 文字字形 -->
				<view class="property-item">
					<text class="property-label">文字字形</text>
					<view class="text-style-controls">
						<view 
							class="style-btn" 
							:class="{active: isBold}"
							@click="toggleBold"
						>
							<text class="style-icon">B</text>
						</view>
						<view 
							class="style-btn" 
							:class="{active: isItalic}"
							@click="toggleItalic"
						>
							<text class="style-icon italic">I</text>
						</view>
						<view 
							class="style-btn" 
							:class="{active: isUnderline}"
							@click="toggleUnderline"
						>
							<text class="style-icon underline">U</text>
						</view>
						<view 
							class="style-btn" 
							:class="{active: isStrikethrough}"
							@click="toggleStrikethrough"
						>
							<text class="style-icon strikethrough">S</text>
						</view>
					</view>
				</view>
				
				<!-- 文字高度拉伸 -->
				<view class="property-item">
					<text class="property-label">高度拉伸</text>
					<view class="slider-control">
						<view class="slider-btn" @click="decreaseHeightScale">-</view>
						<slider 
							:value="heightScale" 
							:min="0.5" 
							:max="2.0" 
							:step="0.1"
							@change="onHeightScaleChange"
							class="property-slider"
							show-value
						/>
						<view class="slider-btn" @click="increaseHeightScale">+</view>
					</view>
					<text class="slider-value">{{heightScale.toFixed(1)}}</text>
				</view>
				
				<!-- 文字字号 -->
				<view class="property-item">
					<text class="property-label">文字字号</text>
					<view class="slider-control">
						<view class="slider-btn" @click="decreaseFontSize">-</view>
						<slider 
							:value="fontSize" 
							:min="8" 
							:max="80" 
							:step="1"
							@change="onFontSizeChange"
							class="property-slider"
							show-value
						/>
						<view class="slider-btn" @click="increaseFontSize">+</view>
					</view>
					<text class="slider-value">{{fontSize}}</text>
				</view>
			</view>
			
			<!-- 保存按钮 -->
			<view class="action-buttons">
				<view class="preview-button" @click="previewTemplate">预 览</view>
				<view class="save-button" @click="saveTemplate">保 存</view>
			</view>
			
		</scroll-view>
		
		<!-- 颜色选择器弹窗 -->
		<view v-if="showColorPicker" class="color-picker-modal" @click="showColorPicker = ''">
			<view class="color-picker-content" @click.stop>
				<view class="color-picker-title">
					<text v-if="showColorPicker === 'text'">{{templateTypeList[templateTypeIndex]}}颜色选择</text>
					<text v-else-if="showColorPicker === 'background'">{{templateTypeList[templateTypeIndex]}}背景颜色</text>
				</view>
				<view class="color-grid">
					<view 
						v-for="color in (showColorPicker === 'text' ? colorList : backgroundColorList)" 
						:key="color"
						class="color-item-container"
					>
						<view 
							class="color-item" 
							:style="{backgroundColor: color}"
							@click="selectColor(color)"
						></view>
						<text class="color-item-name">{{colorNameMap[color] }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 模板文字弹窗 -->
		<uv-popup ref="templateTextPopup" mode="bottom" :closeable="true" @close="onTemplatePopupClose" :round="16" :safeAreaInsetBottom="true">
			<view class="template-popup-content">
				<view class="popup-title">添加模版文字</view>
				
				<view class="template-field">
					<text class="field-label">姓名</text>
					<input v-model="templateFields.name" class="field-input" placeholder="请输入姓名" />
				</view>
				
				<view class="template-field">
					<text class="field-label">职位</text>
					<input v-model="templateFields.position" class="field-input" placeholder="请输入职位" />
				</view>
				
				<view class="template-field">
					<text class="field-label">公司</text>
					<input v-model="templateFields.company" class="field-input" placeholder="请输入公司" />
				</view>
				
				<view class="template-field">
					<text class="field-label">其他信息</text>
					<input v-model="templateFields.other" class="field-input" placeholder="请输入其他信息" />
				</view>
				
				<view class="popup-btns">
					<view class="popup-btn cancel" @click="onTemplatePopupClose">取消</view>
					<view class="popup-btn confirm" @click="confirmAddTemplateText">确认</view>
				</view>
			</view>
		</uv-popup>
		
		<!-- 预览弹窗 -->
		<uv-popup ref="previewPopup" mode="center" :closeable="true" @close="closePreview" :round="16" :safeAreaInsetBottom="true">
			<view class="preview-popup-content">
				<view class="popup-title">模板预览</view>
				
				<view class="preview-image-container">
					<image v-if="previewImagePath" :src="previewImagePath" mode="aspectFit" class="preview-image"></image>
					<view v-else-if="previewLoading" class="preview-loading">
						<text>生成预览中...</text>
					</view>
					<view v-else class="preview-error">
						<text>预览生成失败</text>
					</view>
				</view>
				
				<view class="popup-btns">
					<view class="popup-btn cancel" @click="closePreview">关闭</view>
					<view class="popup-btn confirm" @click="savePreviewImage" v-if="previewImagePath">保存图片</view>
				</view>
			</view>
		</uv-popup>
	</view>
</template>

<script>
	export default {
			onShow() {
		// 检查是否有选择的背景图片
		try {
			const selectedBackgroundImage = uni.getStorageSync('selectedBackgroundImage');
			if (selectedBackgroundImage) {
				console.log('获取到背景图片URL:', selectedBackgroundImage);
				
				// 使用getImageInfo获取图片信息，确保图片可以被正确加载
				uni.getImageInfo({
					src: selectedBackgroundImage,
					success: (res) => {
						console.log('背景图片加载成功:', res.path);
						// 存储图片信息
						this.backgroundImageInfo = res;
						// 使用本地路径作为背景
						this.backgroundImage = res.path;
						// 立即重绘画布
						this.$nextTick(() => {
							this.drawCanvas();
						});
						
						// 提示成功
						uni.showToast({
							title: '背景已应用',
							icon: 'success',
							duration: 1500
						});
					},
					fail: (err) => {
						console.error('背景图片加载失败:', err);
						// 尝试直接使用URL
						this.backgroundImage = selectedBackgroundImage;
						this.backgroundImageInfo = null;
						this.drawCanvas();
						
						uni.showToast({
							title: '背景应用可能不完整',
							icon: 'none'
						});
					},
					complete: () => {
						// 清除存储
						uni.removeStorageSync('selectedBackgroundImage');
					}
				});
			}
		} catch (e) {
			console.error('获取背景图片失败:', e);
		}
	},
		data() {
			return {
				// 模版名称
				templateName: '',
				
				// 背景
				backgroundImage: '',
				backgroundColor: '#FFFFFF',
				
				// 模板文字字段
				templateFields: {
					name: '张三',
					position: '经理',
					company: '某某科技有限公司',
					other: ''
				},
				
				// 模板类型相关
				templateTypeIndex: 0,
				templateTypeList: ['三色桌牌', '四色桌牌', '七色桌牌'],
				colorListByType: {
					// 三色：黑白红
					0: ['#000000', '#FFFFFF', '#FF0000'],
					// 四色：黑白红黄
					1: ['#000000', '#FFFFFF', '#FF0000', '#FFFF00'],
					// 七色：黑、白、红、绿、蓝、黄、橙
					2: ['#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FFA500']
				},
				
				// 颜色名称映射
				colorNameMap: {
					'#000000': '黑色',
					'#FFFFFF': '白色',
					'#FF0000': '红色',
					'#00FF00': '绿色',
					'#0000FF': '蓝色',
					'#FFFF00': '黄色',
					'#FFA500': '橙色'
				},
				
				// 背景颜色相关
				backgroundColorList: ['#000000', '#FFFFFF', '#FF0000'],				
				// 字体相关
				fontFamilyIndex: 0,
				fontFamilyList: ['微软雅黑', '宋体', '黑体', 'Arial', 'Times New Roman'],
				
				// 文字颜色
				textColor: '#000000',
				showColorPicker: '', // 之前是boolean
				colorList: ['#000000', '#FFFFFF', '#FF0000'],
				
				// 文字对齐
				textAlignIndex: 0,
				textAlignList: [
					{ name: '左对齐', value: 'left' },
					{ name: '居中对齐', value: 'center' },
					{ name: '右对齐', value: 'right' }
				],
				
				// 位置对齐
				positionAlignIndex: 0,
				positionAlignList: ['左上', '中上', '右上', '左中', '中中', '右中', '左下', '中下', '右下'],
				
				// 水平和垂直位置
				horizontalAlign: 'left',
				verticalAlign: 'top',
				horizontalPosition: 0,
				verticalPosition: 0,
				horizontalAlignIcons: [
					{icon: '≡', value: 'left'},
					{icon: '☰', value: 'center'},
					{icon: '≣', value: 'right'}
				],
				verticalAlignIcons: [
					{icon: '┬', value: 'top'},
					{icon: '┼', value: 'middle'},
					{icon: '┴', value: 'bottom'}
				],
				
				// 文字样式
				isBold: false,
				isItalic: false,
				isUnderline: false,
				isStrikethrough: false,
				
				// 字号
				fontSize: 16,
				
				// 高度拉伸
				heightScale: 1.0,
				
				// Canvas相关
				canvasContext: null,
				canvasWidth: 300,  // 固定宽度
				canvasHeight: 180, // 固定高度
				canvasElements: [], // 画布上的元素
				selectedElement: null, // 当前选中的元素
				canvasRect: null, // Canvas容器的位置信息
				
				// 触摸相关
				touchStartX: 0,
				touchStartY: 0,
				isDragging: false,
				
				// 多指触摸相关
				lastTouches: null,
				initialDistance: 0,
				isScaling: false,
				
				// 编辑图标位置
				editIconPosition: null,
				deleteIconPosition: null,
				rotateIconPosition: null,
				
				// 批量操作相关
				batchOptions: {
					fontSize: true,
					fontFamily: true,
					textColor: true,
					textAlign: true,
					textStyle: true, // 包括粗体、斜体、下划线、删除线
					heightScale: true
				},
				
				// 预览相关
				showPreview: false,
				previewImagePath: '',
				previewLoading: false,
				backgroundImageInfo: null,
			}
		},
		mounted() {
			this.initCanvas();
			// 监听屏幕旋转或窗口大小变化
			window.addEventListener('resize', this.resizeCanvas);
			
			// 初始化颜色列表
			this.colorList = this.colorListByType[this.templateTypeIndex];
			
			// 初始化背景颜色列表
							this.backgroundColorList = [];
				for (let i = 0; i < this.colorList.length; i++) {
					this.backgroundColorList.push(this.colorList[i]);
				}
		},
		beforeDestroy() {
			// 移除事件监听
			window.removeEventListener('resize', this.resizeCanvas);
		},
		methods: {
			// 初始化Canvas
			initCanvas() {
				const sysInfo = uni.getSystemInfoSync();
				console.log('设备信息:', sysInfo);
				
				// 根据设备屏幕宽度计算画布尺寸
				// 取屏幕宽度的80%，并保持16:9的宽高比
				this.canvasWidth = Math.floor(sysInfo.windowWidth * 0.9);
				this.canvasHeight = Math.floor(this.canvasWidth * 0.6); // 约为16:9的比例
				
				console.log('设置Canvas尺寸:', this.canvasWidth, this.canvasHeight);
				
				// 延迟一下再创建上下文，确保DOM已经渲染
				setTimeout(() => {
					this.canvasContext = uni.createCanvasContext('editCanvas', this);
					// 获取Canvas容器位置信息，用于后续触摸事件坐标转换
					this.updateCanvasRect();
					this.drawCanvas();
				}, 200);
			},
			
			// 获取Canvas容器的位置信息
			updateCanvasRect() {
				const query = uni.createSelectorQuery().in(this);
				query.select('#canvasContainer').boundingClientRect(data => {
					if (data) {
						this.canvasRect = data;
						console.log('Canvas容器位置信息:', data);
					}
				}).exec();
			},
			
			// 调整Canvas大小
			resizeCanvas() {
				const sysInfo = uni.getSystemInfoSync();
				this.canvasWidth = Math.floor(sysInfo.windowWidth * 0.9);
				this.canvasHeight = Math.floor(this.canvasWidth * 0.6);
				
				// 更新Canvas容器位置信息
				this.updateCanvasRect();
				
				// 重新绘制
				if (this.canvasContext) {
					this.drawCanvas();
				} else {
					this.canvasContext = uni.createCanvasContext('editCanvas', this);
					this.drawCanvas();
				}
			},
			
			// 绘制Canvas
			drawCanvas() {
				if (!this.canvasContext) {
					console.log('Canvas上下文不存在，初始化Canvas');
					this.initCanvas();
					return;
				}
				
				console.log('绘制Canvas开始，背景图片路径:', this.backgroundImage);
				
				const ctx = this.canvasContext;
				// 清空画布
				ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
				
				// 绘制背景
				if (this.backgroundImage) {
					try {
						console.log('绘制背景图片:', this.backgroundImage);
						
						// 保持背景图片比例
						if (this.backgroundImageInfo) {
							// 使用已获取的图片信息
							this.drawBackgroundWithRatio(ctx, this.backgroundImage, this.backgroundImageInfo);
						} else {
							// 获取图片信息并绘制
							uni.getImageInfo({
								src: this.backgroundImage,
								success: (imageInfo) => {
									this.backgroundImageInfo = imageInfo;
									this.drawBackgroundWithRatio(ctx, this.backgroundImage, imageInfo);
									// 重新绘制整个画布
									this.drawCanvas();
								},
								fail: (err) => {
									console.error('获取背景图片信息失败:', err);
									// 直接绘制，可能会变形
									ctx.drawImage(this.backgroundImage, 0, 0, this.canvasWidth, this.canvasHeight);
									this.completeDrawing(ctx);
								}
							});
							// 先用背景色填充
							ctx.setFillStyle(this.backgroundColor);
							ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
							return; // 等待异步获取图片信息
						}
					} catch (e) {
						console.error('绘制背景图片失败:', e);
						// 绘制失败时使用背景色
						ctx.setFillStyle(this.backgroundColor);
						ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
					}
				} else {
					// 无背景图时使用背景色
					ctx.setFillStyle(this.backgroundColor);
					ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
				}
				
				this.completeDrawing(ctx);
			},
			
			// 按比例绘制背景图片
			drawBackgroundWithRatio(ctx, imagePath, imageInfo) {
				// 计算图片和画布的宽高比
				const imageRatio = imageInfo.width / imageInfo.height;
				const canvasRatio = this.canvasWidth / this.canvasHeight;
				
				let drawWidth, drawHeight, offsetX = 0, offsetY = 0;
				
				if (imageRatio > canvasRatio) {
					// 图片比画布更宽，以高度为基准，居中显示
					drawHeight = this.canvasHeight;
					drawWidth = drawHeight * imageRatio;
					offsetX = (this.canvasWidth - drawWidth) / 2;
				} else {
					// 图片比画布更高，以宽度为基准，居中显示
					drawWidth = this.canvasWidth;
					drawHeight = drawWidth / imageRatio;
					offsetY = (this.canvasHeight - drawHeight) / 2;
				}
				
				// 先用背景色填充整个画布
				ctx.setFillStyle(this.backgroundColor);
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
				
				// 绘制图片，保持比例并居中
				ctx.drawImage(imagePath, offsetX, offsetY, drawWidth, drawHeight);
			},
			
			// 完成绘制剩余元素
			completeDrawing(ctx) {
				// 绘制所有元素
				this.canvasElements.forEach(element => {
					this.drawElement(element);
				});
				
				if (this.selectedElement) {
					this.drawSelectionHandles(this.selectedElement);
				}
				
				ctx.draw(false, () => {
					console.log('Canvas绘制完成');
				});
			},
			
			// 绘制单个元素
			drawElement(element) {
				const ctx = this.canvasContext;
				ctx.save();
				
				try {
					if (element.type === 'text') {
						ctx.setFillStyle(element.color || '#000000');
						
						const fontStyle = element.isItalic ? 'italic' : 'normal';
						const fontWeight = element.isBold ? 'bold' : 'normal';
						const fontSize = element.fontSize || 16;
						const fontFamily = element.fontFamily || 'sans-serif';
						ctx.font = fontStyle + " " + fontWeight + " " + fontSize + "px " + fontFamily;
						
						// 设置文本对齐方式
						ctx.setTextAlign(element.textAlign || 'left');
						
						// 处理高度拉伸
						if (element.heightScale && element.heightScale !== 1) {
							ctx.scale(1, element.heightScale);
							// 调整Y坐标，考虑高度拉伸的影响
							const scaledY = element.y / element.heightScale;
							ctx.fillText(element.text, element.x, scaledY);
						} else {
							ctx.fillText(element.text, element.x, element.y);
						}
						
						// 绘制下划线或删除线
						if(element.isUnderline || element.isStrikethrough) {
							const metrics = ctx.measureText(element.text);
							const textWidth = metrics.width;
							let lineY = element.y;
							
							// 如果有高度拉伸，需要调整线条位置
							if (element.heightScale && element.heightScale !== 1) {
								lineY = element.y / element.heightScale;
							}
							
							if (element.isUnderline) {
								lineY = lineY + 2;
							}
							if (element.isStrikethrough) {
								lineY = lineY - fontSize / 3;
							}
							
							// 考虑文本对齐方式
							let lineStartX = element.x;
							if (element.textAlign === 'center') {
								lineStartX = element.x - textWidth / 2;
							} else if (element.textAlign === 'right') {
								lineStartX = element.x - textWidth;
							}
							
							ctx.beginPath();
							ctx.setStrokeStyle(element.color || '#000000');
							ctx.setLineWidth(1);
							ctx.moveTo(lineStartX, lineY);
							ctx.lineTo(lineStartX + textWidth, lineY);
							ctx.stroke();
						}
					} else if (element.type === 'image') {
						// 检查图片是否超出画布边界，并调整位置和大小
						let x = element.x;
						let y = element.y;
						let width = element.width;
						let height = element.height;
						
						// 确保图片不会超出右侧和底部边界
						if (x + width > this.canvasWidth) {
							x = this.canvasWidth - width;
						}
						
						if (y + height > this.canvasHeight) {
							y = this.canvasHeight - height;
						}
						
						// 确保x和y不会为负值（超出左侧和顶部边界）
						x = Math.max(0, x);
						y = Math.max(0, y);
						
						// 更新元素位置，确保下次绘制时位置正确
						if (x !== element.x || y !== element.y) {
							element.x = x;
							element.y = y;
						}
						
						// 应用旋转
						if (element.rotation && element.rotation !== 0) {
							// 将原点移动到图片中心
							const centerX = x + width / 2;
							const centerY = y + height / 2;
							ctx.translate(centerX, centerY);
							// 旋转画布
							ctx.rotate(element.rotation * Math.PI / 180);
							// 将原点移回去并绘制图片
							ctx.drawImage(element.src, -width / 2, -height / 2, width, height);
						} else {
							// 无旋转时正常绘制图片
							ctx.drawImage(element.src, x, y, width, height);
						}
					}
				} catch (e) {
					console.error("绘制元素错误:", e);
				}
				
				ctx.restore();
			},

			drawSelectionHandles(element) {
				const ctx = this.canvasContext;
				const box = this.getElementBoundingBox(element);
				
				// 保存当前上下文状态
				ctx.save();
				
				// 设置边框样式
				ctx.setStrokeStyle('#0969da');
				ctx.setLineWidth(2);
				
				// 如果图片已旋转，需要旋转边框
				if (element.type === 'image' && element.rotation && element.rotation !== 0) {
					const centerX = element.x + element.width / 2;
					const centerY = element.y + element.height / 2;
					
					ctx.translate(centerX, centerY);
					ctx.rotate(element.rotation * Math.PI / 180);
					
					// 绘制旋转后的边框
					const halfWidth = element.width / 2;
					const halfHeight = element.height / 2;
					ctx.strokeRect(-halfWidth, -halfHeight, element.width, element.height);
					
					// 重置旋转以绘制控制图标
					ctx.rotate(-element.rotation * Math.PI / 180);
					ctx.translate(-centerX, -centerY);
				} else {
					// 绘制边框
					ctx.strokeRect(box.x, box.y, box.width, box.height);
				}
				
				// 图标半径
				const iconRadius = 18;
				
				// 根据元素类型决定显示哪些图标
				if (element.type === 'image') {
					// 图片元素只显示删除和旋转图标
					
					// 删除图标位置 - 右上角
					const deleteIconX = box.x + box.width + iconRadius;
					const deleteIconY = box.y - iconRadius;
					
					// 旋转图标位置 - 左上角
					const rotateIconX = box.x - iconRadius;
					const rotateIconY = box.y - iconRadius;
					
					// 存储图标位置信息，用于触摸事件检测
					this.deleteIconPosition = {
						x: deleteIconX,
						y: deleteIconY,
						radius: iconRadius * 1.5 // 增大点击区域
					};
					
					this.rotateIconPosition = {
						x: rotateIconX,
						y: rotateIconY,
						radius: iconRadius * 1.5 // 增大点击区域
					};
					
					// 编辑图标位置设为null，表示没有该图标
					this.editIconPosition = null;
					
					// 绘制删除图标背景
					ctx.beginPath();
					ctx.setFillStyle('#f44336');
					ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
					ctx.fill();
					
					// 绘制旋转图标背景
					ctx.beginPath();
					ctx.setFillStyle('#0969da');
					ctx.arc(rotateIconX, rotateIconY, iconRadius, 0, 2 * Math.PI);
					ctx.fill();
					
					// 绘制删除图标（X形状）
					ctx.beginPath();
					ctx.setStrokeStyle('#ffffff');
					ctx.setLineWidth(2.5);
					ctx.moveTo(deleteIconX - 7, deleteIconY - 7);
					ctx.lineTo(deleteIconX + 7, deleteIconY + 7);
					ctx.moveTo(deleteIconX + 7, deleteIconY - 7);
					ctx.lineTo(deleteIconX - 7, deleteIconY + 7);
					ctx.stroke();
					
					// 绘制旋转图标（圆形箭头）
					ctx.beginPath();
					ctx.setStrokeStyle('#ffffff');
					ctx.setLineWidth(2);
					ctx.arc(rotateIconX, rotateIconY, iconRadius * 0.6, 0.3 * Math.PI, 2.2 * Math.PI);
					ctx.stroke();
					
					// 绘制箭头尖
					ctx.beginPath();
					ctx.moveTo(rotateIconX + 6, rotateIconY - 6);
					ctx.lineTo(rotateIconX, rotateIconY - 10);
					ctx.lineTo(rotateIconX - 4, rotateIconY - 6);
					ctx.setFillStyle('#ffffff');
					ctx.fill();
					
				} else {
					// 文本元素显示编辑和删除图标
					// 编辑图标位置 - 左上角
					const editIconX = box.x - iconRadius;
					const editIconY = box.y - iconRadius;
					// 删除图标位置 - 右上角
					const deleteIconX = box.x + box.width + iconRadius;
					const deleteIconY = box.y - iconRadius;
					
					// 存储图标位置信息，用于触摸事件检测
					this.editIconPosition = {
						x: editIconX,
						y: editIconY,
						radius: iconRadius * 1.5 // 增大点击区域
					};
					
					this.deleteIconPosition = {
						x: deleteIconX,
						y: deleteIconY,
						radius: iconRadius * 1.5 // 增大点击区域
					};
					
					// 旋转图标位置设为null，表示没有该图标
					this.rotateIconPosition = null;
					
					// 绘制编辑图标背景
					ctx.beginPath();
					ctx.setFillStyle('#0969da');
					ctx.arc(editIconX, editIconY, iconRadius, 0, 2 * Math.PI);
					ctx.fill();
					
					// 绘制删除图标背景
					ctx.beginPath();
					ctx.setFillStyle('#f44336');
					ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
					ctx.fill();
					
					// 绘制编辑图标（简单的铅笔图标）
					ctx.setFillStyle('#ffffff');
					ctx.setStrokeStyle('#ffffff');
					ctx.setLineWidth(2);
					// 绘制铅笔图标
					ctx.beginPath();
					ctx.moveTo(editIconX - 7, editIconY + 7);
					ctx.lineTo(editIconX + 5, editIconY - 5);
					ctx.lineTo(editIconX + 7, editIconY - 3);
					ctx.lineTo(editIconX - 5, editIconY + 9);
					ctx.closePath();
					ctx.fill();
					
					// 绘制删除图标（X形状）
					ctx.beginPath();
					ctx.setLineWidth(2.5);
					ctx.moveTo(deleteIconX - 7, deleteIconY - 7);
					ctx.lineTo(deleteIconX + 7, deleteIconY + 7);
					ctx.moveTo(deleteIconX + 7, deleteIconY - 7);
					ctx.lineTo(deleteIconX - 7, deleteIconY + 7);
					ctx.stroke();
				}
				
				// 恢复上下文状态
				ctx.restore();
			},
			
			getElementBoundingBox(element) {
				if (element.type === 'image') {
					// 如果图片已旋转，我们仍返回未旋转的边界框
					// 旋转的处理在绘制时进行
					return {
						x: element.x,
						y: element.y,
						width: element.width,
						height: element.height
					};
				} else if (element.type === 'text') {
					// 文本元素需要特殊处理，因为y坐标是基线位置
					const ctx = this.canvasContext;
					const fontStyle = element.isItalic ? 'italic' : 'normal';
					const fontWeight = element.isBold ? 'bold' : 'normal';
					const fontSize = element.fontSize || 16;
					const fontFamily = element.fontFamily || 'sans-serif';
					
					// 设置字体以便测量
					ctx.font = fontStyle + " " + fontWeight + " " + fontSize + "px " + fontFamily;
					
					// 获取文本宽度
					let width = 0;
					try {
						const metrics = ctx.measureText(element.text);
						width = metrics.width;
					} catch (e) {
						// 如果测量失败，使用一个估算值
						width = element.text.length * fontSize * 0.6;
						console.error('测量文本宽度失败:', e);
					}
					
					// 考虑文本对齐方式
					let x = element.x;
					if (element.textAlign === 'center') {
						x = element.x - width / 2;
					} else if (element.textAlign === 'right') {
						x = element.x - width;
					}
					
					// 文本的边界框：从基线上方fontSize像素到基线下方少量像素
					return {
						x: x,
						y: element.y - fontSize, // 文本基线上方
						width: width,
						height: fontSize * 1.5 // 大约是字体大小的1.5倍，给予更多空间
					};
				}
				
				// 默认返回一个最小的边界框
				return { 
					x: element.x,
					y: element.y,
					width: 10,
					height: 10
				};
			},

			// Canvas容器触摸事件
			onContainerTouchStart(e) {
				// 阻止默认行为，避免滚动干扰
				e.preventDefault && e.preventDefault();
				
				// 多指触摸处理
				if (e.touches.length === 2 && this.selectedElement && this.selectedElement.type === 'image') {
					// 双指操作 - 初始化缩放
					this.isScaling = true;
					this.isDragging = false;
					
					// 记录初始两点距离
					const touch1 = e.touches[0];
					const touch2 = e.touches[1];
					const dx = touch1.clientX - touch2.clientX;
					const dy = touch1.clientY - touch2.clientY;
					this.initialDistance = Math.sqrt(dx * dx + dy * dy);
					
					// 记录触摸点
					this.lastTouches = [];
					for (let i = 0; i < e.touches.length; i++) {
						this.lastTouches.push(e.touches[i]);
					}
					return;
				}
				
				// 单指触摸处理
				// 记录触摸开始信息
				const touch = e.touches[0];
				
				// 计算相对于Canvas的坐标
				if (!this.canvasRect) {
					this.updateCanvasRect();
					return;
				}
				
				// 相对于屏幕的坐标减去Canvas容器的位置，得到相对于Canvas容器的坐标
				const canvasX = touch.clientX - this.canvasRect.left;
				const canvasY = touch.clientY - this.canvasRect.top;
				
				console.log('触摸坐标:', canvasX, canvasY);
				
				// 保存起始位置
				this.touchStartX = canvasX;
				this.touchStartY = canvasY;
				
				// 首先检查是否点击了操作图标
				if (this.selectedElement) {
					// 检查是否点击了删除图标
					if (this.deleteIconPosition && this.isPointInCircle(canvasX, canvasY, this.deleteIconPosition)) {
						console.log('点击了删除图标');
						setTimeout(() => {
							this.deleteSelectedElement();
						}, 100);
						return;
					}
					
					// 检查是否点击了编辑图标
					if (this.editIconPosition && this.isPointInCircle(canvasX, canvasY, this.editIconPosition)) {
						console.log('点击了编辑图标');
						setTimeout(() => {
							this.showElementEditPopup();
						}, 100);
						return;
					}
					
					// 检查是否点击了旋转图标
					if (this.rotateIconPosition && this.isPointInCircle(canvasX, canvasY, this.rotateIconPosition)) {
						console.log('点击了旋转图标');
						setTimeout(() => {
							this.rotateSelectedElement();
						}, 100);
						return;
					}
				}
				
				// 检查点击的元素
				this.selectElementAt(canvasX, canvasY);
				
				if (this.selectedElement) {
					this.isDragging = true;
					this.updateInspector();
				}
			},
			
			// 旋转选中的元素
			rotateSelectedElement() {
				if (!this.selectedElement || this.selectedElement.type !== 'image') return;
				
				// 如果元素没有rotation属性，初始化为0
				if (this.selectedElement.rotation === undefined) {
					this.selectedElement.rotation = 0;
				}
				
				// 每次旋转45度
				this.selectedElement.rotation = (this.selectedElement.rotation + 45) % 360;
				
				// 重绘画布
				this.drawCanvas();
				
				// 显示提示
				uni.showToast({
					title: `已旋转至${this.selectedElement.rotation}°`,
					icon: 'none',
					duration: 1500
				});
			},
			
			// 判断点是否在圆内
			isPointInCircle(x, y, circle) {
				const dx = x - circle.x;
				const dy = y - circle.y;
				const distance = Math.sqrt(dx * dx + dy * dy);
				console.log('点到圆心距离:', distance, '圆半径:', circle.radius);
				return distance <= circle.radius;
			},
			
			// 显示元素编辑弹窗
			showElementEditPopup() {
				// 针对不同类型的元素显示不同的编辑弹窗
				if (this.selectedElement.type === 'text') {
					// 创建一个输入框，用于编辑文本内容
					uni.showModal({
						title: '编辑文本',
						editable: true,
						content: this.selectedElement.text || '',
						placeholderText: '请输入文本内容',
						confirmText: '确定',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 只有当内容不为空时才更新文本内容
								if (res.content && res.content.trim() !== '') {
									this.selectedElement.text = res.content.trim();
									this.drawCanvas();
								} else {
									uni.showToast({
										title: '文本内容不能为空',
										icon: 'none'
									});
								}
							}
						}
					});
				} else if (this.selectedElement.type === 'image') {
					// 对于图片，提供更多操作选项
					uni.showActionSheet({
						itemList: ['更换图片', '调整大小', '旋转图片'],
						success: (res) => {
							if (res.tapIndex === 0) {
								// 更换图片
								this.replaceSelectedImage();
							} else if (res.tapIndex === 1) {
								// 调整图片大小的逻辑
								this.resizeSelectedImage();
							} else if (res.tapIndex === 2) {
								// 旋转图片
								uni.showToast({
									title: '旋转功能开发中',
									icon: 'none'
								});
							}
						}
					});
				}
			},
			
			// 调整选中图片的大小
			resizeSelectedImage() {
				if (!this.selectedElement || this.selectedElement.type !== 'image') return;
				
				// 获取当前图片尺寸
				const currentWidth = this.selectedElement.width;
				const currentHeight = this.selectedElement.height;
				const currentX = this.selectedElement.x;
				const currentY = this.selectedElement.y;
				
				// 计算宽高比
				const ratio = currentWidth / currentHeight;
				
				uni.showModal({
					title: '调整图片大小',
					content: '请选择调整操作',
					showCancel: true,
					cancelText: '缩小',
					confirmText: '放大',
					success: (res) => {
						let newWidth, newHeight;
						
						if (res.confirm) {
							// 放大20%
							newWidth = Math.round(currentWidth * 1.2);
							newHeight = Math.round(currentHeight * 1.2);
						} else if (res.cancel) {
							// 缩小20%
							newWidth = Math.round(currentWidth * 0.8);
							newHeight = Math.round(currentHeight * 0.8);
						} else {
							return; // 用户取消，不做任何操作
						}
						
						// 检查调整后的尺寸是否会超出画布边界
						let newX = currentX;
						let newY = currentY;
						
						// 如果放大后会超出右边界，调整X坐标
						if (newX + newWidth > this.canvasWidth) {
							newX = Math.max(0, this.canvasWidth - newWidth);
						}
						
						// 如果放大后会超出下边界，调整Y坐标
						if (newY + newHeight > this.canvasHeight) {
							newY = Math.max(0, this.canvasHeight - newHeight);
						}
						
						// 更新元素尺寸和位置
						this.selectedElement.width = newWidth;
						this.selectedElement.height = newHeight;
						this.selectedElement.x = newX;
						this.selectedElement.y = newY;
						
						// 重绘画布
						this.drawCanvas();
					}
				});
			},
			
			// 替换选中的图片
			replaceSelectedImage() {
				if (!this.selectedElement || this.selectedElement.type !== 'image') return;
				
				uni.chooseImage({
					count: 1,
					success: (res) => {
						const imagePath = res.tempFilePaths[0];
						uni.getImageInfo({
							src: imagePath,
							success: (imageInfo) => {
								// 保持原有位置
								const currentX = this.selectedElement.x;
								const currentY = this.selectedElement.y;
								
								// 计算新图片尺寸，保持宽高比
								const originalWidth = this.selectedElement.width;
								const originalHeight = this.selectedElement.height;
								const ratio = imageInfo.width / imageInfo.height;
								
								let newWidth, newHeight;
								
								if (ratio > 1) {
									// 宽图，固定宽度
									newWidth = originalWidth;
									newHeight = originalWidth / ratio;
								} else {
									// 高图，固定高度
									newHeight = originalHeight;
									newWidth = originalHeight * ratio;
								}
								
								// 确保新尺寸不超过画布大小
								if (newWidth > this.canvasWidth) {
									newWidth = this.canvasWidth;
									newHeight = newWidth / ratio;
								}
								
								if (newHeight > this.canvasHeight) {
									newHeight = this.canvasHeight;
									newWidth = newHeight * ratio;
								}
								
								// 检查新尺寸下是否会超出画布边界，如果会，调整位置
								let newX = currentX;
								let newY = currentY;
								
								// 如果超出右边界，调整X坐标
								if (newX + newWidth > this.canvasWidth) {
									newX = Math.max(0, this.canvasWidth - newWidth);
								}
								
								// 如果超出下边界，调整Y坐标
								if (newY + newHeight > this.canvasHeight) {
									newY = Math.max(0, this.canvasHeight - newHeight);
								}
								
								// 更新图片属性
								this.selectedElement.src = imagePath;
								this.selectedElement.width = newWidth;
								this.selectedElement.height = newHeight;
								this.selectedElement.x = newX;
								this.selectedElement.y = newY;
								
								this.drawCanvas();
							}
						});
					}
				});
			},
			
			// 删除选中的元素
			deleteSelectedElement() {
				if (!this.selectedElement) return;
				
				const elementType = this.selectedElement.type === 'text' ? '文本' : '图片';
				
				uni.showModal({
					title: '确认删除',
					content: `确定要删除这个${elementType}元素吗？`,
					confirmColor: '#f44336',
					success: (res) => {
						if (res.confirm) {
							// 从元素数组中删除
							const index = this.canvasElements.findIndex(item => item.id === this.selectedElement.id);
							if (index !== -1) {
								// 保存要删除的元素
								const deletedElement = this.canvasElements[index];
								
								// 从数组中移除
								this.canvasElements.splice(index, 1);
								this.selectedElement = null; // 清除选中状态
								
								// 重绘画布
								this.drawCanvas();
								
								// 显示成功提示
								uni.showToast({
									title: elementType + "已删除",
									icon: 'success',
									duration: 1500
								});
							}
						}
					}
				});
			},
			
			onContainerTouchMove(e) {
				// 多指触摸处理 - 缩放
				if (e.touches.length === 2 && this.isScaling && this.selectedElement && this.selectedElement.type === 'image') {
					// 计算当前两点距离
					const touch1 = e.touches[0];
					const touch2 = e.touches[1];
					const dx = touch1.clientX - touch2.clientX;
					const dy = touch1.clientY - touch2.clientY;
					const currentDistance = Math.sqrt(dx * dx + dy * dy);
					
					// 计算缩放比例
					const scaleFactor = currentDistance / this.initialDistance;
					
					// 计算缩放中心点
					const centerX = (touch1.clientX + touch2.clientX) / 2 - this.canvasRect.left;
					const centerY = (touch1.clientY + touch2.clientY) / 2 - this.canvasRect.top;
					
					// 应用缩放
					if (scaleFactor > 0.1) { // 防止缩放太小
						const originalWidth = this.selectedElement.width / (this.lastScaleFactor || 1);
						const originalHeight = this.selectedElement.height / (this.lastScaleFactor || 1);
						
						// 计算新的尺寸
						let newWidth = originalWidth * scaleFactor;
						let newHeight = originalHeight * scaleFactor;
						
						// 限制最小尺寸
						const minSize = 30;
						if (newWidth < minSize) {
							newWidth = minSize;
							newHeight = (minSize / originalWidth) * originalHeight;
						}
						if (newHeight < minSize) {
							newHeight = minSize;
							newWidth = (minSize / originalHeight) * originalWidth;
						}
						
						// 限制最大尺寸
						const maxWidth = this.canvasWidth * 0.95;
						const maxHeight = this.canvasHeight * 0.95;
						if (newWidth > maxWidth) {
							newWidth = maxWidth;
							newHeight = (maxWidth / originalWidth) * originalHeight;
						}
						if (newHeight > maxHeight) {
							newHeight = maxHeight;
							newWidth = (maxHeight / originalHeight) * originalWidth;
						}
						
						// 更新元素尺寸
						this.selectedElement.width = newWidth;
						this.selectedElement.height = newHeight;
						
						// 保存当前缩放因子
						this.lastScaleFactor = scaleFactor;
						
						// 调整位置，使元素围绕缩放中心点缩放
						const elementCenterX = this.selectedElement.x + this.selectedElement.width / 2;
						const elementCenterY = this.selectedElement.y + this.selectedElement.height / 2;
						
						// 计算新位置
						let newX = this.selectedElement.x;
						let newY = this.selectedElement.y;
						
						// 如果元素位置会超出画布边界，调整位置
						if (newX < 0) newX = 0;
						if (newY < 0) newY = 0;
						if (newX + newWidth > this.canvasWidth) newX = this.canvasWidth - newWidth;
						if (newY + newHeight > this.canvasHeight) newY = this.canvasHeight - newHeight;
						
						this.selectedElement.x = newX;
						this.selectedElement.y = newY;
						
						// 重绘画布
						this.drawCanvas();
					}
					
					// 更新最后的触摸点
					this.lastTouches = [];
					for (let i = 0; i < e.touches.length; i++) {
						this.lastTouches.push(e.touches[i]);
					}
					return;
				}
				
				// 单指拖动处理
				if (!this.isDragging || !this.selectedElement || !this.canvasRect) return;
				
				// 阻止默认滚动行为
				e.preventDefault && e.preventDefault();
				
				const touch = e.touches[0];
				
				// 计算相对于Canvas的坐标
				const canvasX = touch.clientX - this.canvasRect.left;
				const canvasY = touch.clientY - this.canvasRect.top;
				
				// 计算移动距离
				const deltaX = canvasX - this.touchStartX;
				const deltaY = canvasY - this.touchStartY;
				
				// 计算新位置
				let newX = this.selectedElement.x + deltaX;
				let newY = this.selectedElement.y + deltaY;
				
				// 获取元素尺寸
				let elementWidth, elementHeight;
				
				if (this.selectedElement.type === 'image') {
					// 图片元素直接使用width和height
					elementWidth = this.selectedElement.width;
					elementHeight = this.selectedElement.height;
				} else {
					// 其他元素使用边界框
					const elementBox = this.getElementBoundingBox(this.selectedElement);
					elementWidth = elementBox.width;
					elementHeight = elementBox.height;
				}
				
				// 边界检查 - 确保元素不会超出画布
				// 左边界检查
				if (newX < 0) {
					newX = 0;
				}
				
				// 上边界检查
				if (this.selectedElement.type === 'text') {
					// 文本元素需要考虑基线位置
					if (newY < elementHeight) {
						newY = elementHeight;
					}
				} else {
					// 图片等其他元素
					if (newY < 0) {
						newY = 0;
					}
				}
				
				// 右边界检查
				if (newX + elementWidth > this.canvasWidth) {
					newX = this.canvasWidth - elementWidth;
				}
				
				// 下边界检查
				if (newY + elementHeight > this.canvasHeight) {
					newY = this.canvasHeight - elementHeight;
				}
				
				// 更新元素位置
				this.selectedElement.x = newX;
				this.selectedElement.y = newY;
				
				// 更新UI显示的位置
				this.horizontalPosition = Math.round(newX);
				this.verticalPosition = Math.round(newY);
				
				// 更新起始位置，让移动更连贯
				this.touchStartX = canvasX;
				this.touchStartY = canvasY;
				
				// 重绘Canvas
				this.drawCanvas();
			},
			
			onContainerTouchEnd(e) {
				this.isDragging = false;
				this.isScaling = false;
				this.lastScaleFactor = 1;
				this.lastTouches = null;
			},

			updateInspector() {
				if (this.selectedElement) {
					const el = this.selectedElement;
					this.horizontalPosition = Math.round(el.x);
					this.verticalPosition = Math.round(el.y);

					if (el.type === 'text') {
						// 设置字体
						this.fontFamilyIndex = this.fontFamilyList.indexOf(el.fontFamily || '微软雅黑');
						if (this.fontFamilyIndex === -1) this.fontFamilyIndex = 0;
						
						// 设置颜色
						this.textColor = el.color || '#000000';
						
						// 设置文本对齐
						this.textAlignIndex = this.textAlignList.findIndex(item => item.value === (el.textAlign || 'left'));
						if (this.textAlignIndex === -1) this.textAlignIndex = 0;
						
						// 设置文本样式
						this.isBold = !!el.isBold;
						this.isItalic = !!el.isItalic;
						this.isUnderline = !!el.isUnderline;
						this.isStrikethrough = !!el.isStrikethrough;
						
						// 设置字号和高度拉伸
						this.fontSize = parseInt(el.fontSize || 16);
						this.heightScale = parseFloat(el.heightScale || 1.0);
						
						// 延迟更新，确保在UI显示之后处理
						setTimeout(() => {
							this.drawCanvas();
						}, 50);
					}
				}
			},

			updateElementPosition() {
				if(this.selectedElement) {
					this.selectedElement.x = this.horizontalPosition;
					this.selectedElement.y = this.verticalPosition;
					this.drawCanvas();
				}
			},
			
			// 字体相关方法
			onFontFamilyChange(e) {
				this.fontFamilyIndex = e.detail.value;
				this.updateSelectedElement('fontFamily', this.fontFamilyList[this.fontFamilyIndex]);
			},
			
			onTextAlignChange(e) {
				this.textAlignIndex = e.detail.value;
				this.updateSelectedElement('textAlign', this.textAlignList[this.textAlignIndex].value);
			},
			
			onPositionAlignChange(e) {
				this.positionAlignIndex = e.detail.value;
				// Position alignment logic needs to be implemented based on canvas dimensions
				// this.updateSelectedElement('positionAlign', this.positionAlignList[this.positionAlignIndex]);
			},
			
			// 位置对齐方法
			setHorizontalAlign(value) {
				this.horizontalAlign = value;
				
				if (this.selectedElement) {
					// 有选中元素时，只对选中元素进行操作
					// 获取元素的边界框，用于计算新位置
					const box = this.getElementBoundingBox(this.selectedElement);
					let newX = this.selectedElement.x;
					
					// 根据选择的对齐方式设置新的水平位置
					if (value === 'left') {
						newX = 0; // 左对齐到画布左边缘
					} else if (value === 'center') {
						// 居中对齐：画布中心减去元素宽度的一半
						if (this.selectedElement.type === 'text' && this.selectedElement.textAlign === 'center') {
							// 如果文本已经是居中对齐，直接放在画布中心
							newX = this.canvasWidth / 2;
						} else if (this.selectedElement.type === 'text' && this.selectedElement.textAlign === 'right') {
							// 如果文本是右对齐，需要考虑文本宽度
							newX = this.canvasWidth / 2 + box.width / 2;
						} else {
							// 其他情况
							newX = (this.canvasWidth - box.width) / 2;
						}
					} else if (value === 'right') {
						// 右对齐：画布右边缘减去元素宽度
						if (this.selectedElement.type === 'text' && this.selectedElement.textAlign === 'right') {
							// 如果文本是右对齐，直接放在画布右边缘
							newX = this.canvasWidth;
						} else if (this.selectedElement.type === 'text' && this.selectedElement.textAlign === 'center') {
							// 如果文本是居中对齐，需要考虑文本宽度
							newX = this.canvasWidth - box.width / 2;
						} else {
							// 其他情况
							newX = this.canvasWidth - box.width;
						}
					}
					
					// 更新元素位置
					this.selectedElement.x = newX;
					this.horizontalPosition = Math.round(newX);
					this.drawCanvas();
				} else {
					// 没有选中元素时，对所有文本元素应用水平对齐
					let updated = false;
					
					// 检查是否应该应用此属性
					if (!this.batchOptions.textAlign) {
						return; // 如果批量选项中未选择对齐方式，则不执行
					}
					
					// 遍历所有文本元素
					this.canvasElements.forEach(element => {
						if (element.type === 'text') {
							const box = this.getElementBoundingBox(element);
							let newX = element.x;
							
							// 根据选择的对齐方式设置新的水平位置
							if (alignValue === 'left') {
								newX = 0; // 左对齐到画布左边缘
							} else if (alignValue === 'center') {
								// 居中对齐
								if (element.textAlign === 'center') {
									newX = this.canvasWidth / 2;
								} else if (element.textAlign === 'right') {
									newX = this.canvasWidth / 2 + box.width / 2;
								} else {
									newX = (this.canvasWidth - box.width) / 2;
								}
							} else if (alignValue === 'right') {
								// 右对齐
								if (element.textAlign === 'right') {
									newX = this.canvasWidth;
								} else if (element.textAlign === 'center') {
									newX = this.canvasWidth - box.width / 2;
								} else {
									newX = this.canvasWidth - box.width;
								}
							}
							
							// 更新元素位置
							element.x = newX;
							updated = true;
						}
					});
					
					// 如果有元素被更新，重绘画布
					if (updated) {
						this.drawCanvas();
						uni.showToast({
							title: `已应用${message}`,
							icon: 'success',
							duration: 1500
						});
					}
				}
			},
			
			setVerticalAlign(value) {
				this.verticalAlign = value;
				
				if (this.selectedElement) {
					// 有选中元素时，只对选中元素进行操作
					// 获取元素的边界框，用于计算新位置
					const box = this.getElementBoundingBox(this.selectedElement);
					let newY = this.selectedElement.y;
					
					// 根据选择的对齐方式设置新的垂直位置
					if (value === 'top') {
						if (this.selectedElement.type === 'text') {
							// 文本需要考虑基线位置，y坐标是文本底部
							newY = box.height; // 顶部对齐，确保文本完全可见
						} else {
							newY = 0; // 顶部对齐到画布顶部
						}
					} else if (value === 'middle') {
						if (this.selectedElement.type === 'text') {
							// 文本垂直居中需要考虑基线位置
							newY = this.canvasHeight / 2 + box.height / 2;
						} else {
							// 图片等元素居中
							newY = (this.canvasHeight - box.height) / 2;
						}
					} else if (value === 'bottom') {
						if (this.selectedElement.type === 'text') {
							// 文本底部对齐
							newY = this.canvasHeight;
						} else {
							// 图片等元素底部对齐
							newY = this.canvasHeight - box.height;
						}
					}
					
					// 更新元素位置
					this.selectedElement.y = newY;
					this.verticalPosition = Math.round(newY);
					this.drawCanvas();
				} else {
					// 没有选中元素时，对所有文本元素应用垂直对齐
					let updated = false;
					
					// 检查是否应该应用此属性
					if (!this.batchOptions.textAlign) {
						return; // 如果批量选项中未选择对齐方式，则不执行
					}
					
					// 遍历所有文本元素
					this.canvasElements.forEach(element => {
						if (element.type === 'text') {
							const box = this.getElementBoundingBox(element);
							let newY = element.y;
							
							// 根据选择的对齐方式设置新的垂直位置
							if (value === 'top') {
								// 文本需要考虑基线位置
								newY = box.height; // 顶部对齐
							} else if (value === 'middle') {
								// 文本垂直居中
								newY = this.canvasHeight / 2 + box.height / 2;
							} else if (value === 'bottom') {
								// 文本底部对齐
								newY = this.canvasHeight;
							}
							
							// 更新元素位置
							element.y = newY;
							updated = true;
						}
					});
					
					// 如果有元素被更新，重绘画布
					if (updated) {
						this.drawCanvas();
						uni.showToast({
							title: '已应用到所有文本',
							icon: 'none',
							duration: 1000
						});
					}
				}
			},
			
			// 文字样式切换
			toggleBold() {
				this.isBold = !this.isBold;
				this.updateSelectedElement('isBold', this.isBold);
			},
			
			toggleItalic() {
				this.isItalic = !this.isItalic;
				this.updateSelectedElement('isItalic', this.isItalic);
			},
			
			toggleUnderline() {
				this.isUnderline = !this.isUnderline;
				this.updateSelectedElement('isUnderline', this.isUnderline);
			},
			
			toggleStrikethrough() {
				this.isStrikethrough = !this.isStrikethrough;
				this.updateSelectedElement('isStrikethrough', this.isStrikethrough);
			},
			
			// 字号调整
			increaseFontSize() {
				if (this.fontSize < 80) {
					this.fontSize = parseInt(this.fontSize) + 2;
					this.updateSelectedElement('fontSize', this.fontSize);
				}
			},
			
			decreaseFontSize() {
				if (this.fontSize > 8) {
					this.fontSize = parseInt(this.fontSize) - 2;
					this.updateSelectedElement('fontSize', this.fontSize);
				}
			},
			
			onFontSizeChange(e) {
				// slider的change事件返回的是一个事件对象，我们需要从中获取值
				const value = e.detail ? e.detail.value : e;
				this.fontSize = parseInt(value);
				this.updateSelectedElement('fontSize', this.fontSize);
			},

			updateFontSize() {
				this.updateSelectedElement('fontSize', this.fontSize);
			},

			// 颜色选择
			selectColor(color) {
				// 根据当前颜色选择模式，检查颜色是否在允许的列表中
				if (this.showColorPicker === 'text') {
					// 文本颜色模式
					if (!this.colorList.includes(color)) {
						uni.showToast({
							title: `当前模板类型不支持该颜色`,
							icon: 'none',
							duration: 1500
						});
						return;
					}
					
					this.textColor = color;
					this.updateSelectedElement('color', color);
				} else if (this.showColorPicker === 'background') {
					// 背景颜色模式
					if (!this.backgroundColorList.includes(color)) {
						uni.showToast({
							title: `当前模板类型不支持该背景颜色`,
							icon: 'none',
							duration: 1500
						});
						return;
					}
					
					this.backgroundColor = color;
					this.backgroundImage = '';
					this.drawCanvas();
				}
				this.showColorPicker = '';
			},
			
			// 更新选中元素
			updateSelectedElement(property, value) {
				// 如果是颜色属性，检查是否在允许的颜色列表中
				if (property === 'color' && !this.colorList.includes(value)) {
					uni.showToast({
						title: `当前模板类型不支持该颜色`,
						icon: 'none',
						duration: 1500
					});
					return;
				}
				
				if (this.selectedElement) {
					// 更新元素属性
					this.selectedElement[property] = value;
					
					// 确保数据类型正确
					if (property === 'fontSize') {
						this.selectedElement[property] = parseInt(value);
						this.fontSize = this.selectedElement[property];
					} else if (property === 'heightScale') {
						this.selectedElement[property] = parseFloat(value);
						this.heightScale = this.selectedElement[property];
					}
					
					// 当更新字体相关属性时，同步到UI状态
					if (property === 'isBold') this.isBold = value;
					if (property === 'isItalic') this.isItalic = value;
					if (property === 'isUnderline') this.isUnderline = value;
					if (property === 'isStrikethrough') this.isStrikethrough = value;
					if (property === 'textAlign') {
						this.textAlignIndex = this.textAlignList.findIndex(item => item.value === value);
					}
					if (property === 'fontFamily') {
						this.fontFamilyIndex = this.fontFamilyList.indexOf(value);
					}
					if (property === 'color') {
						this.textColor = value;
					}
					
					// 重绘画布
					this.drawCanvas();
					
					// 打印调试信息
					console.log(`属性 ${property} 已更新为:`, value);
				} else {
					// 没有选中元素时，更新所有文本元素
					let updated = false;
					
					// 更新默认设置
					if (property === 'fontSize') {
						this.fontSize = parseInt(value);
					} else if (property === 'heightScale') {
						this.heightScale = parseFloat(value);
					} else if (property === 'isBold') {
						this.isBold = value;
					} else if (property === 'isItalic') {
						this.isItalic = value;
					} else if (property === 'isUnderline') {
						this.isUnderline = value;
					} else if (property === 'isStrikethrough') {
						this.isStrikethrough = value;
					} else if (property === 'textAlign') {
						this.textAlignIndex = this.textAlignList.findIndex(item => item.value === value);
					} else if (property === 'fontFamily') {
						this.fontFamilyIndex = this.fontFamilyList.indexOf(value);
					} else if (property === 'color') {
						this.textColor = value;
					}
					
					// 检查是否应该应用此属性
					let shouldApply = true;
					if (property === 'fontSize' && !this.batchOptions.fontSize) shouldApply = false;
					if ((property === 'fontFamily') && !this.batchOptions.fontFamily) shouldApply = false;
					if (property === 'color' && !this.batchOptions.textColor) shouldApply = false;
					if (property === 'textAlign' && !this.batchOptions.textAlign) shouldApply = false;
					if ((property === 'isBold' || property === 'isItalic' || 
						property === 'isUnderline' || property === 'isStrikethrough') && 
						!this.batchOptions.textStyle) shouldApply = false;
					if (property === 'heightScale' && !this.batchOptions.heightScale) shouldApply = false;
					
					// 只有当选择了应用此属性时才进行批量更新
					if (shouldApply) {
						// 遍历所有文本元素并更新属性
						this.canvasElements.forEach(element => {
							if (element.type === 'text') {
								// 只更新文本元素的属性
								element[property] = value;
								
								// 确保数据类型正确
								if (property === 'fontSize') {
									element[property] = parseInt(value);
								} else if (property === 'heightScale') {
									element[property] = parseFloat(value);
								}
								
								updated = true;
							}
						});
						
						// 如果有元素被更新，重绘画布
						if (updated) {
							this.drawCanvas();
							uni.showToast({
								title: '已应用到所有文本',
								icon: 'none',
								duration: 1000
							});
						}
					}
					
					console.log(`默认${property}已更新为:`, value);
				}
			},
			
			// 水平居中所有元素
			centerAllElements() {
				// 检查是否有文本元素
				const textElements = this.canvasElements.filter(element => element.type === 'text');
				if (textElements.length === 0) {
					uni.showToast({
						title: '没有可居中的文本元素',
						icon: 'none',
						duration: 1500
					});
					return;
				}
				
				// 显示对齐方式选择
				uni.showActionSheet({
					itemList: ['水平居中', '左对齐', '右对齐', '统一文本对齐方式'],
					success: (res) => {
						let alignValue;
						let textAlignValue;
						let message;
						
						switch(res.tapIndex) {
							case 0: // 水平居中
								alignValue = 'center';
								message = '水平居中';
								break;
							case 1: // 左对齐
								alignValue = 'left';
								message = '左对齐';
								break;
							case 2: // 右对齐
								alignValue = 'right';
								message = '右对齐';
								break;
							case 3: // 统一文本对齐方式
								// 二级菜单，选择文本内部对齐方式
								uni.showActionSheet({
									itemList: ['左对齐', '居中对齐', '右对齐'],
									success: (subRes) => {
										switch(subRes.tapIndex) {
											case 0:
												textAlignValue = 'left';
												message = '文本左对齐';
												break;
											case 1:
												textAlignValue = 'center';
												message = '文本居中对齐';
												break;
											case 2:
												textAlignValue = 'right';
												message = '文本右对齐';
												break;
											default:
												return;
										}
										
										// 应用文本内部对齐
										let updated = false;
										textElements.forEach(element => {
											element.textAlign = textAlignValue;
											updated = true;
										});
										
										if (updated) {
											this.drawCanvas();
											uni.showToast({
												title: `已应用${message}`,
												icon: 'success',
												duration: 1500
											});
										}
									}
								});
								return;
							default:
								return;
						}
						
						// 应用水平位置对齐
						if (alignValue) {
							let updated = false;
							
							textElements.forEach(element => {
								const box = this.getElementBoundingBox(element);
								let newX = element.x;
								
								// 根据选择的对齐方式设置新的水平位置
								if (alignValue === 'left') {
									newX = 0; // 左对齐到画布左边缘
								} else if (alignValue === 'center') {
									// 居中对齐
									if (element.textAlign === 'center') {
										newX = this.canvasWidth / 2;
									} else if (element.textAlign === 'right') {
										newX = this.canvasWidth / 2 + box.width / 2;
									} else {
										newX = (this.canvasWidth - box.width) / 2;
									}
								} else if (alignValue === 'right') {
									// 右对齐
									if (element.textAlign === 'right') {
										newX = this.canvasWidth;
									} else if (element.textAlign === 'center') {
										newX = this.canvasWidth - box.width / 2;
									} else {
										newX = this.canvasWidth - box.width;
									}
								}
								
								// 更新元素位置
								element.x = newX;
								updated = true;
							});
							
							if (updated) {
								this.drawCanvas();
								uni.showToast({
									title: `已应用${message}`,
									icon: 'success',
									duration: 1500
								});
							}
						}
					}
				});
			},
			
			autoAdjustSpacing() {
				// 检查是否有文本元素
				const textElements = this.canvasElements.filter(element => element.type === 'text');
				if (textElements.length <= 1) {
					uni.showToast({
						title: '需要至少两个文本元素',
						icon: 'none',
						duration: 1500
					});
					return;
				}
				
				// 显示行间距模式选择
				uni.showActionSheet({
					itemList: ['均匀分布', '紧凑排列', '宽松排列'],
					success: (res) => {
						// 按Y坐标排序文本元素（从上到下）
						const sortedElements = [];
						for (let i = 0; i < textElements.length; i++) {
							sortedElements.push(textElements[i]);
						}
						sortedElements.sort((a, b) => {
							// 获取元素的边界框
							const boxA = this.getElementBoundingBox(a);
							const boxB = this.getElementBoundingBox(b);
							return boxA.y - boxB.y;
						});
						
						// 计算可用的垂直空间
						const totalHeight = this.canvasHeight;
						
						// 计算所有元素的总高度
						let totalElementsHeight = 0;
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							totalElementsHeight += box.height;
						});
						
						// 根据选择的模式设置间距
						let spacingFactor;
						let spacingMode;
						
						switch(res.tapIndex) {
							case 0: // 均匀分布
								spacingFactor = 1;
								spacingMode = '均匀分布';
								break;
							case 1: // 紧凑排列
								spacingFactor = 0.5;
								spacingMode = '紧凑排列';
								break;
							case 2: // 宽松排列
								spacingFactor = 1.5;
								spacingMode = '宽松排列';
								break;
							default:
								spacingFactor = 1;
								spacingMode = '均匀分布';
						}
						
						// 计算每个元素之间的间距（根据选择的模式）
						const elementCount = sortedElements.length;
						const baseSpacing = (totalHeight - totalElementsHeight) / (elementCount + 1);
						const spacing = baseSpacing * spacingFactor;
						
						// 确保间距不小于最小值
						const minSpacing = 10; // 最小间距为10像素
						const actualSpacing = Math.max(spacing, minSpacing);
						
						// 如果使用宽松排列且元素太多，可能需要调整位置以确保所有元素都在画布内
						let startY = actualSpacing;
						const totalSpaceNeeded = totalElementsHeight + actualSpacing * (elementCount + 1);
						
						if (totalSpaceNeeded > totalHeight) {
							// 如果总空间超出画布高度，调整起始位置
							startY = Math.max(minSpacing, (totalHeight - totalElementsHeight - actualSpacing * (elementCount - 1)) / 2);
						}
						
						// 重新定位所有元素
						let currentY = startY; // 从顶部间距开始
						
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							
							// 计算新的Y坐标
							let newY;
							if (element.type === 'text') {
								// 文本元素的Y坐标是基线位置
								newY = currentY + box.height;
							} else {
								newY = currentY;
							}
							
							// 更新元素位置
							element.y = newY;
							
							// 更新当前Y坐标
							currentY += box.height + actualSpacing;
						});
						
						// 重绘画布
						this.drawCanvas();
						
						uni.showToast({
							title: `已应用${spacingMode}间距`,
							icon: 'success',
							duration: 1500
						});
					}
				});
			},
			beautifyAllElements() {
				// 检查是否有文本元素
				const textElements = this.canvasElements.filter(element => element.type === 'text');
				if (textElements.length === 0) {
					uni.showToast({
						title: '没有可美化的文本元素',
						icon: 'none',
						duration: 1500
					});
					return;
				}
				
				// 显示美化模式选择
				uni.showActionSheet({
					itemList: ['标准美化', '简约风格', '强调风格'],
					success: (res) => {
						// 按Y坐标排序文本元素（从上到下）
						const sortedElements = [];
						for (let i = 0; i < textElements.length; i++) {
							sortedElements.push(textElements[i]);
						}
						sortedElements.sort((a, b) => {
							// 获取元素的边界框
							const boxA = this.getElementBoundingBox(a);
							const boxB = this.getElementBoundingBox(b);
							return boxA.y - boxB.y;
						});
						
						// 根据选择的美化模式设置参数
						let spacingFactor, fontSizeMultiplier, mainColor, titleFontSize;
						let mode; 
						switch(res.tapIndex) {
							case 0: // 标准美化
								spacingFactor = 1;
								fontSizeMultiplier = 1;
								mainColor = '#333333';
								titleFontSize = 24;
								mode = '标准美化';
								break;
							case 1: // 简约风格
								spacingFactor = 0.8;
								fontSizeMultiplier = 0.9;
								mainColor = '#555555';
								titleFontSize = 22;
								mode = '简约风格';
								break;
							case 2: // 强调风格
								spacingFactor = 1.2;
								fontSizeMultiplier = 1.1;
								mainColor = '#000000';
								titleFontSize = 28;
								mode = '强调风格';
								break;
							default:
								spacingFactor = 1;
								fontSizeMultiplier = 1;
								mainColor = '#333333';
								titleFontSize = 24;
								mode = '标准美化';
						}
						
						// 1. 水平居中所有文本元素
						sortedElements.forEach((element, index) => {
							// 获取元素的边界框
							const box = this.getElementBoundingBox(element);
							
							// 设置水平居中
							if (element.textAlign === 'center') {
								element.x = this.canvasWidth / 2;
							} else if (element.textAlign === 'right') {
								element.x = this.canvasWidth / 2 + box.width / 2;
							} else {
								element.x = (this.canvasWidth - box.width) / 2;
							}
							
							// 2. 设置统一的文本对齐方式为居中
							element.textAlign = 'center';
							
							// 3. 根据位置设置不同的字体大小和样式
							if (index === 0) {
								// 第一个元素（标题）
								element.fontSize = titleFontSize;
								element.isBold = true;
								element.color = mainColor;
							} else if (index === 1) {
								// 第二个元素（副标题）
								element.fontSize = Math.floor(titleFontSize * 0.8);
								element.isBold = false;
								element.color = mainColor;
							} else {
								// 其他元素
								element.fontSize = Math.floor(titleFontSize * 0.7);
								element.color = mainColor;
							}
							
							// 应用字体大小乘数
							element.fontSize = Math.floor(element.fontSize * fontSizeMultiplier);
						});
						
						// 4. 调整元素间距
						// 计算可用的垂直空间
						const totalHeight = this.canvasHeight;
						
						// 计算所有元素的总高度
						let totalElementsHeight = 0;
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							totalElementsHeight += box.height;
						});
						
						// 计算每个元素之间的间距
						const elementCount = sortedElements.length;
						const spacing = ((totalHeight - totalElementsHeight) / (elementCount + 1)) * spacingFactor;
						
						// 确保间距不小于最小值
						const minSpacing = 10;
						const actualSpacing = Math.max(spacing, minSpacing);
						
						// 重新定位所有元素
						let currentY = actualSpacing;
						
						sortedElements.forEach(element => {
							const box = this.getElementBoundingBox(element);
							
							// 计算新的Y坐标
							let newY;
							if (element.type === 'text') {
								newY = currentY + box.height;
							} else {
								newY = currentY;
							}
							
							// 更新元素位置
							element.y = newY;
							
							// 更新当前Y坐标
							currentY += box.height + actualSpacing;
						});
						
						// 5. 更新UI状态
						this.textAlignIndex = this.textAlignList.findIndex(item => item.value === 'center');
						this.textColor = mainColor;
						
						// 6. 重绘画布
						this.drawCanvas();
						
						uni.showToast({
							title: `已应用${mode}`,
							icon: 'success',
							duration: 1500
						});
					}
				});
			},
			previewTemplate() {
				// 设置加载状态
				this.previewLoading = true;
				this.previewImagePath = '';
				
				// 确保画布已经绘制完成
				this.drawCanvas();
				
				// 延迟一下再生成图片，确保Canvas已经完全渲染
				setTimeout(() => {
					// 将Canvas转换为图片
					uni.canvasToTempFilePath({
						canvasId: 'editCanvas',
						success: (res) => {
							console.log('生成预览图片成功:', res.tempFilePath);
							this.previewImagePath = res.tempFilePath;
							this.previewLoading = false;
							
							// 打开预览弹窗
							this.$refs.previewPopup.open();
						},
						fail: (err) => {
							console.error('生成预览图片失败:', err);
							this.previewLoading = false;
							
							// 显示错误提示
							uni.showToast({
								title: '预览生成失败',
								icon: 'none',
								duration: 2000
							});
						}
					}, this);
				}, 200);
			},
			closePreview() {
				this.$refs.previewPopup.close();
			},
			savePreviewImage() {
				if (!this.previewImagePath) {
					uni.showToast({
						title: '预览图片不存在',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				
				// 显示加载提示
				uni.showLoading({
					title: '保存中...'
				});
				
				// 保存图片到相册
				uni.saveImageToPhotosAlbum({
					filePath: this.previewImagePath,
					success: () => {
						uni.hideLoading();
						uni.showToast({
							title: '图片已保存到相册',
							icon: 'success',
							duration: 2000
						});
					},
					fail: (err) => {
						uni.hideLoading();
						console.error('保存图片失败:', err);
						
						// 检查是否是因为用户拒绝授权
						if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('authorize') >= 0) {
							uni.showModal({
								title: '提示',
								content: '保存图片需要授权，是否去设置页面授权？',
								success: (res) => {
									if (res.confirm) {
										// 打开设置页面
										uni.openSetting();
									}
								}
							});
						} else {
							uni.showToast({
								title: '保存失败',
								icon: 'none',
								duration: 2000
							});
						}
					}
				});
			},
			// 模板类型选择
			onTemplateTypeChange(e) {
				this.templateTypeIndex = e.detail.value;
				// 更新颜色列表
				this.colorList = this.colorListByType[this.templateTypeIndex];
				
				// 更新背景颜色列表 - 添加默认的灰色背景，然后添加当前模板类型允许的所有颜色
				this.backgroundColorList = [];
				for (let i = 0; i < this.colorList.length; i++) {
					this.backgroundColorList.push(this.colorList[i]);
				}
				
				// 如果当前颜色不在新的颜色列表中，重置为黑色
				if (!this.colorList.includes(this.textColor)) {
					this.textColor = '#000000';
					
					// 如果有选中的元素，也更新元素的颜色
					if (this.selectedElement && this.selectedElement.type === 'text') {
						this.selectedElement.color = this.textColor;
						this.drawCanvas();
					}
				}
				
				// 如果当前背景颜色不在新的背景颜色列表中，重置为默认
				if (!this.backgroundColorList.includes(this.backgroundColor)) {
					this.backgroundColor = '#ffffff';
					this.drawCanvas();
				}
				
				// 显示提示
				uni.showToast({
					title: `已切换至${this.templateTypeList[this.templateTypeIndex]}`,
					icon: 'none',
					duration: 1500
				});
			},
			// 背景相关方法
			setSolidBackground() {
				this.showColorPicker = 'background';
			},
			
			// 清除背景
			clearBackground() {
				this.backgroundImage = '';
				this.backgroundColor = '#FFFFFF';
				this.drawCanvas();
				
				uni.showToast({
					title: '已清除背景',
					icon: 'success',
					duration: 1500
				});
			},
			
			// 选择背景图片
			selectBackground() {
				// 跳转到背景列表页面
				uni.navigateTo({
					url: '/pages/template/backgroundList?select=true'
				});
			},
			
			// 插入模板文字
			insertTemplateText() {
				// 打开模板文字弹窗
				this.$refs.templateTextPopup.open();
			},
			
			// 关闭模板文字弹窗
			onTemplatePopupClose() {
				this.$refs.templateTextPopup.close();
			},
			
			// 确认添加模板文字
			confirmAddTemplateText() {
				// 添加姓名文本
				if (this.templateFields.name) {
					this.addTextElement(this.templateFields.name, 24, true);
				}
				
				// 添加职位文本
				if (this.templateFields.position) {
					this.addTextElement(this.templateFields.position, 18);
				}
				
				// 添加公司文本
				if (this.templateFields.company) {
					this.addTextElement(this.templateFields.company, 16);
				}
				
				// 添加其他信息文本
				if (this.templateFields.other) {
					this.addTextElement(this.templateFields.other, 14);
				}
				
				// 关闭弹窗
				this.$refs.templateTextPopup.close();
				
				// 提示成功
				uni.showToast({
					title: '已添加模板文字',
					icon: 'success',
					duration: 1500
				});
			},
			
			// 插入固定文字
			insertFixedText() {
				uni.showModal({
					title: '添加文本',
					editable: true,
					placeholderText: '请输入文本内容',
					success: (res) => {
						if (res.confirm && res.content && res.content.trim() !== '') {
							this.addTextElement(res.content.trim());
						}
					}
				});
			},
			
			// 添加文本元素
			addTextElement(text, fontSize = 16, isBold = false) {
				// 生成唯一ID
				const id = 'text_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
				
				// 计算文本位置 - 默认放在画布中心
				const x = this.canvasWidth / 2;
				const y = this.canvasHeight / 2;
				
				// 创建文本元素
				const textElement = {
					id,
					type: 'text',
					text,
					x,
					y,
					fontSize,
					fontFamily: this.fontFamilyList[this.fontFamilyIndex],
					color: this.textColor,
					textAlign: 'center',
					isBold,
					isItalic: false,
					isUnderline: false,
					isStrikethrough: false,
					heightScale: 1.0
				};
				
				// 添加到元素数组
				this.canvasElements.push(textElement);
				
				// 选中新添加的元素
				this.selectedElement = textElement;
				
				// 更新属性面板
				this.updateInspector();
				
				// 重绘画布
				this.drawCanvas();
			},
			
			// 插入图片
			insertImage() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						const imagePath = res.tempFilePaths[0];
						
						// 获取图片信息
						uni.getImageInfo({
							src: imagePath,
							success: (imageInfo) => {
								// 生成唯一ID
								const id = 'image_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
								
								// 计算图片尺寸，保持宽高比
								const maxWidth = this.canvasWidth * 0.8;
								const maxHeight = this.canvasHeight * 0.8;
								
								let width = imageInfo.width;
								let height = imageInfo.height;
								
								// 如果图片太大，按比例缩小
								if (width > maxWidth || height > maxHeight) {
									const ratio = Math.min(maxWidth / width, maxHeight / height);
									width = Math.floor(width * ratio);
									height = Math.floor(height * ratio);
								}
								
								// 计算图片位置 - 默认放在画布中心
								const x = (this.canvasWidth - width) / 2;
								const y = (this.canvasHeight - height) / 2;
								
								// 创建图片元素
								const imageElement = {
									id,
									type: 'image',
									src: imagePath,
									x,
									y,
									width,
									height,
									rotation: 0 // 初始旋转角度为0
								};
								
								// 添加到元素数组
								this.canvasElements.push(imageElement);
								
								// 选中新添加的元素
								this.selectedElement = imageElement;
								
								// 重绘画布
								this.drawCanvas();
								
								// 提示成功
								uni.showToast({
									title: '已添加图片',
									icon: 'success',
									duration: 1500
								});
							},
							fail: (err) => {
								console.error('获取图片信息失败:', err);
								uni.showToast({
									title: '添加图片失败',
									icon: 'none',
									duration: 1500
								});
							}
						});
					}
				});
			},
			
			// 选择元素
			selectElementAt(x, y) {
				// 首先清除当前选中状态
				this.selectedElement = null;
				
				// 从后往前遍历元素，这样可以优先选择最上层的元素
				for (let i = this.canvasElements.length - 1; i >= 0; i--) {
					const element = this.canvasElements[i];
					const box = this.getElementBoundingBox(element);
					
					// 检查点击位置是否在元素的边界框内
					if (x >= box.x && x <= box.x + box.width && y >= box.y && y <= box.y + box.height) {
						// 找到了被点击的元素
						this.selectedElement = element;
						break;
					}
				}
				
				// 重绘画布，显示选中状态
				this.drawCanvas();
				
				return this.selectedElement;
			},
			
			// 保存模板
			saveTemplate() {
				// 检查模板名称
				if (!this.templateName.trim()) {
					uni.showModal({
						title: '提示',
						content: '请输入模板名称',
						showCancel: false
					});
					return;
				}
				
				// 显示加载提示
				uni.showLoading({
					title: '保存中...'
				});
				
				// 将Canvas转换为图片
				uni.canvasToTempFilePath({
					canvasId: 'editCanvas',
					success: (res) => {
						console.log('生成模板图片成功:', res.tempFilePath);
						
						// 构建模板数据
						const templateData = {
							id: 'template_' + Date.now(),
							name: this.templateName.trim(),
							type: this.templateTypeIndex,
							typeName: this.templateTypeList[this.templateTypeIndex],
							backgroundColor: this.backgroundColor,
							backgroundImage: this.backgroundImage,
							elements: this.canvasElements,
							preview: res.tempFilePath,
							createTime: new Date().toISOString()
						};
						
						// 获取已有模板
						const templates = uni.getStorageSync('templates') || [];
						
						// 添加新模板
						templates.push(templateData);
						
						// 保存到本地存储
						uni.setStorageSync('templates', templates);
						
						// 隐藏加载提示
						uni.hideLoading();
						
						// 显示成功提示
						uni.showToast({
							title: '保存成功',
							icon: 'success',
							duration: 2000
						});
						
						// 延迟返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					},
					fail: (err) => {
						console.error('生成模板图片失败:', err);
						uni.hideLoading();
						
						uni.showModal({
							title: '保存失败',
							content: '生成模板图片失败，请重试',
							showCancel: false
						});
					}
				}, this);
			},
			
			// 高度拉伸相关方法
			onHeightScaleChange(e) {
				const value = e.detail ? e.detail.value : e;
				this.heightScale = parseFloat(value);
				this.updateSelectedElement('heightScale', this.heightScale);
			},
			
			increaseHeightScale() {
				if (this.heightScale < 2.0) {
					this.heightScale = parseFloat((this.heightScale + 0.1).toFixed(1));
					this.updateSelectedElement('heightScale', this.heightScale);
				}
			},
			
			decreaseHeightScale() {
				if (this.heightScale > 0.5) {
					this.heightScale = parseFloat((this.heightScale - 0.1).toFixed(1));
					this.updateSelectedElement('heightScale', this.heightScale);
				}
			},
		}
	}
</script>

<style scoped>
	.edit-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f9fa;
		overflow: hidden;
	}
	
	/* Canvas画布区域 */
	.canvas-container {
		padding: 10px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #ffffff;
	}
	
	.edit-canvas {
		background-color: #f5f5f5;
		border: 1px solid #e0e0e0;
	}
	
	/* 分隔线 */
	.divider {
		height: 1px;
		background-color: #e0e0e0;
		width: 100%;
	}
	
	/* 属性设置界面区域 */
	.property-section {
		flex: 1;
		padding: 10px;
		background-color: #ffffff;
		overflow-y: auto;
		overflow-x: hidden;
		-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
		height: 100%;
		box-sizing: border-box;
	}
	
	/* 模版名称区域 */
	.template-name-section {
		margin-bottom: 20px;
		padding: 0 5px;
	}
	
	.template-name-input {
		/* width: 100%; */
		height: 40px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		padding: 0 12px;
		font-size: 14px;
		background-color: #f6f8fa;
	}
	
	/* 属性设置标题 */
	.property-title {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20px;
		color: #24292f;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.property-subtitle {
		font-size: 14px;
		font-weight: normal;
		color: #57606a;
		margin-top: 5px;
	}
	
	/* 属性组 */
	.property-group {
		margin-bottom: 30px;
		padding-bottom: 20px;
		overflow: visible;
	}
	
	/* 属性项 */
	.property-item {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		padding: 10px 5px;
		flex-wrap: wrap; /* 允许在小屏幕上换行 */
	}
	
	.property-label {
		width: 80px;
		font-size: 14px;
		color: #24292f;
		flex-shrink: 0;
	}
	
	/* 选择器样式 */
	.property-picker {
		flex: 1;
		margin-left: 10px;
	}
	
	.picker-view {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 35px;
		line-height: 35px;
		padding: 0 12px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		background-color: #f6f8fa;
		font-size: 14px;
		color: #656d76;
	}
	
	.picker-arrow {
		font-size: 12px;
	}
	
	/* 颜色选择器 */
	.color-picker {
		flex: 1;
		margin-left: 10px;
		display: flex;
		align-items: center;
	}
	
	.color-preview {
		width: 35px;
		height: 35px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		cursor: pointer;
	}
	
	/* 位置控制 */
	.position-controls {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 10px;
		gap: 8px;
	}
	
	.position-icon {
		width: 30px;
		height: 30px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f6f8fa;
		cursor: pointer;
		font-size: 12px;
	}
	
	.position-icon.active {
		background-color: #0969da;
		color: white;
		border-color: #0969da;
	}
	
	.align-icon {
		font-size: 14px;
		font-weight: bold;
	}
	
	/* 文字样式控制 */
	.text-style-controls {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 10px;
		gap: 8px;
	}
	
	.style-btn {
		width: 30px;
		height: 30px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f6f8fa;
		cursor: pointer;
	}
	
	.style-btn.active {
		background-color: #0969da;
		color: white;
		border-color: #0969da;
	}
	
	.style-icon {
		font-size: 14px;
		font-weight: bold;
	}
	
	.style-icon.italic {
		font-style: italic;
	}
	
	.style-icon.underline {
		text-decoration: underline;
	}
	
	.style-icon.strikethrough {
		text-decoration: line-through;
	}
	
	/* 字号控制 */
	.slider-control {
		display: flex;
		align-items: center;
		flex: 1;
		margin-left: 10px;
		gap: 8px;
		min-height: 36px; /* 确保有足够的触摸区域 */
	}
	
	.slider-btn {
		width: 30px;
		height: 30px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f6f8fa;
		cursor: pointer;
		z-index: 2; /* 确保按钮在最上层 */
	}
	
	.slider-value {
		width: 40px;
		text-align: center;
		font-size: 14px;
		margin-left: auto;
		margin-right: 10px;
	}
	
	.property-slider {
		flex: 1;
		margin: 0 5px;
		z-index: 1;
	}
	
	/* 底部控制区域 */
	.bottom-controls {
		margin-top: 30px;
		padding-top: 20px;
		border-top: 1px solid #e0e0e0;
	}
	
	.control-group {
		margin-bottom: 20px;
	}
	
	.group-title {
		font-size: 14px;
		font-weight: bold;
		color: #24292f;
		margin-bottom: 10px;
		display: block;
	}
	
	.button-row {
		display: flex;
		gap: 10px;
		flex-wrap: wrap;
	}
	
	.control-btn {
		padding: 8px 16px;
		border: 1px solid #d0d7de;
		border-radius: 6px;
		background-color: #f6f8fa;
		font-size: 14px;
		color: #24292f;
		cursor: pointer;
		transition: all 0.2s;
		flex: 1;
		min-width: 80px;
		text-align: center;
	}
	
	.control-btn:hover {
		background-color: #f3f4f6;
		border-color: #8c959f;
	}
	
	.control-btn:active {
		background-color: #e5e7ea;
	}
	
	/* 颜色选择器弹窗 */
	.color-picker-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}
	
	.color-picker-content {
		background-color: white;
		border-radius: 8px;
		padding: 20px;
		max-width: 300px;
		width: 90%;
	}
	
	.color-picker-title {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20px;
		color: #24292f;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.color-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 15px;
		justify-content: center;
	}
	
	.color-item-container {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.color-item {
		width: 40px;
		height: 40px;
		border: 1px solid #d0d7de;
		border-radius: 4px;
		cursor: pointer;
		transition: transform 0.2s;
	}
	
	.color-item:hover {
		transform: scale(1.1);
	}
	
	.color-item-name {
		margin-top: 5px;
		font-size: 12px;
		color: #666;
		text-align: center;
	}
	
	/* 保存按钮样式 */
	.action-buttons {
		display: flex;
		justify-content: space-between;
		width: 90%;
		margin: 30px auto 40px auto;
	}
	
	.save-button {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		background-color: #8B0000; /* 深红色 */
		color: white;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		margin-left: 10px;
	}
	
	/* 预览按钮样式 */
	.preview-button {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		background-color: #007bff; /* 浅蓝色 */
		color: white;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		margin-right: 10px;
	}
	
	/* 批量操作选项 */
	.batch-options {
		margin: 10px 0 20px 0;
		padding: 10px 15px;
		background-color: #f6f8fa;
		border-radius: 8px;
		border: 1px solid #d0d7de;
	}
	
	.batch-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 8px 0;
		font-size: 14px;
		color: #0969da;
		cursor: pointer;
	}
	
	.batch-option-text {
		font-weight: 500;
	}
	
	.batch-option-arrow {
		font-size: 12px;
		margin-left: 5px;
	}
	
	.special-btn {
		background-color: #8B0000; /* 深红色 */
		color: white;
		border-color: #8B0000;
		font-weight: bold;
	}
	
	.special-btn:hover {
		background-color: #a52a2a; /* 红褐色 */
		border-color: #a52a2a;
	}
	
	/* 预览弹窗样式 */
	.preview-popup-content {
		width: 90vw;
		height: 80vh;
		padding: 30rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		background-color: #ffffff;
		border-radius: 16rpx;
	}
	
	.preview-image-container {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 8rpx;
		margin: 20rpx 0;
		padding: 10rpx;
		overflow: hidden;
	}
	
	.preview-image {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	}
	
	.preview-loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
	}
	
	.preview-loading:before {
		content: "";
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 20rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #3498db;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.preview-error {
		font-size: 28rpx;
		color: #ff4d4f;
		text-align: center;
		margin-top: 20rpx;
	}
	
	.popup-btns {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
	}
	
	.popup-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
	}
	
	.popup-btn.cancel {
		background-color: #f6f8fa;
		border: 1rpx solid #d0d7de;
		color: #24292f;
		margin-right: 20rpx;
	}
	
	.popup-btn.confirm {
		background-color: #0969da;
		color: white;
		margin-left: 20rpx;
	}
	
	/* 模板类型选择样式 */
	.template-type-section {
		display: flex;
		align-items: center;
		padding: 10px 5px;
		margin-bottom: 15px;
		background-color: #f6f8fa;
		border-radius: 8px;
	}
	
	.template-type-label {
		width: 80px;
		font-size: 14px;
		color: #24292f;
		flex-shrink: 0;
		font-weight: 500;
	}
	
	.template-type-picker {
		flex: 1;
	}
	
	.color-name {
		margin-left: 10px;
		flex: 1;
		font-size: 14px;
		color: #666;
	}
	
	/* 模板文字弹窗样式 */
	.template-popup-content {
		width: 100%;
		height: 66vh; /* 屏幕高度的2/3 */
		padding: 30rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
	}
	
	.popup-title {
		font-size: 36rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 40rpx;
		color: #24292f;
		padding-top: 20rpx;
	}
	
	.template-field {
		margin-bottom: 30rpx;
	}
	
	.field-label {
		display: block;
		font-size: 28rpx;
		color: #24292f;
		margin-bottom: 10rpx;
		font-weight: 500;
	}
	
	.field-input {
		width: 100%;
		height: 80rpx;
		border: 1rpx solid #d0d7de;
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		background-color: #f6f8fa;
	}
	
	.popup-btns {
		display: flex;
		justify-content: space-between;
		margin-top: auto;
		padding-top: 40rpx;
		padding-bottom: 30rpx;
	}
	
	.popup-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
	}
	
	.popup-btn.cancel {
		background-color: #f6f8fa;
		border: 1rpx solid #d0d7de;
		color: #24292f;
		margin-right: 20rpx;
	}
	
	.popup-btn.confirm {
		background-color: #0969da;
		color: white;
		margin-left: 20rpx;
	}
</style>

