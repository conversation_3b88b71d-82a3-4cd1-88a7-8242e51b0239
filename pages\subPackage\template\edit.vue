<template>
	<view class="edit-container">
		<!-- Canvas画布区域 -->
		<view class="canvas-area">
			<canvas 
				ref="canvas"
				canvas-id="templateCanvas" 
				class="canvas" 
				:style="{backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none', backgroundColor: backgroundColor}"
				@touchstart="canvasTouchStart"
				@touchmove="canvasTouchMove"
				@touchend="canvasTouchEnd"
			></canvas>
		</view>
		
		<!-- 模板名称区域 -->
		<view class="template-name-area">
			<text class="template-label">模板名称</text>
			<input class="template-name-input" v-model="templateName" placeholder="新增模板" />
		</view>
		
		<!-- 分隔线 -->
		<view class="divider"></view>
		
		<!-- 对象插入标题 -->
		<view class="section-title">对象插入</view>
			
		<!-- 对象插入按钮组 -->
		<view class="button-group">
			<view class="action-btn primary" @click="openTemplateTextPopup">模版文字</view>
			<view class="action-btn" @click="insertFixedText">固定文字</view>
			<view class="action-btn" @click="insertImage">图片</view>
		</view>
		
		<!-- 背景选择标题 -->
		<view class="section-title">背景选择</view>
		
		<!-- 背景选择按钮组 -->
		<view class="button-group">
			<view class="action-btn primary" @click="selectBackground">选择背景</view>
			<view class="action-btn" @click="selectSolidColor">纯色背景</view>
			<view class="action-btn" @click="clearBackground">清除背景</view>
		</view>
		
		<!-- 分隔线 -->
		<view class="divider"></view>
		
		<!-- 属性设置区域 -->
		<view class="property-area" v-if="selectedElement">
			<view class="property-header">
				<view class="property-title">属性设置</view>
				<view class="element-type-badge" :class="selectedElement.type">
					<uv-icon :name="selectedElement.type === 'text' ? 'edit-pen' : 'image'" size="12"></uv-icon>
					<text>{{ selectedElement.type === 'text' ? '文本' : '图片' }}</text>
				</view>
			</view>
			
			<!-- 属性设置面板 -->
			<view class="property-panel">
				<!-- 基础设置 -->
				<view class="property-section">
					<view class="section-header">
						<uv-icon name="setting" size="16" color="#8B1538"></uv-icon>
						<text>基础设置</text>
					</view>
					
					<!-- 自定义文字 -->
					<view class="property-item" v-if="selectedElement.type === 'text' && selectedElement.text.includes('自定义')">
						<view class="property-label">
							<uv-icon name="edit-pen" size="14" color="#666"></uv-icon>
							<text>自定义文字</text>
						</view>
						<input class="property-input enhanced" v-model="customText" placeholder="请输入自定义文字" @input="updateElementText" />
					</view>
					
					<!-- 文字字体 -->
					<view class="property-item" v-if="selectedElement.type === 'text'">
						<view class="property-label">
							<uv-icon name="font" size="14" color="#666"></uv-icon>
							<text>字体</text>
						</view>
						<view class="property-selector enhanced" @click="toggleFontSelector">
							<text class="selector-text">{{ selectedFont.name }}</text>
							<uv-icon name="arrow-down" size="14" :class="{rotate: showFontSelector}" color="#8B1538"></uv-icon>
						</view>
						<view v-if="showFontSelector" class="selector-options font-options">
							<view v-for="font in availableFonts" :key="font.value" class="option-item" :class="{active: selectedFont.value === font.value}" @click="selectFont(font)">
								<text :style="{fontFamily: font.value}">{{ font.name }}</text>
								<uv-icon v-if="selectedFont.value === font.value" name="checkmark" size="16" color="#fff"></uv-icon>
							</view>
						</view>
					</view>
					
					<!-- 文字颜色 -->
					<view class="property-item" v-if="selectedElement.type === 'text'">
						<view class="property-label">
							<uv-icon name="color-palette" size="14" color="#666"></uv-icon>
							<text>颜色</text>
						</view>
						<view class="property-selector enhanced" @click="toggleColorSelector">
							<view class="color-display">
								<view class="color-preview enhanced" :style="{backgroundColor: selectedColor.value}"></view>
								<text class="selector-text">{{ selectedColor.name }}</text>
							</view>
							<uv-icon name="arrow-down" size="14" :class="{rotate: showColorSelector}" color="#8B1538"></uv-icon>
						</view>
						<view v-if="showColorSelector" class="selector-options color-options">
							<view class="color-grid">
								<view v-for="color in availableColors" :key="color.value" class="color-item enhanced" :class="{active: selectedColor.value === color.value}" @click="selectColor(color)">
									<view class="color-swatch" :style="{backgroundColor: color.value}">
										<uv-icon v-if="selectedColor.value === color.value" name="checkmark" size="12" color="#fff"></uv-icon>
									</view>
									<text class="color-name">{{ color.name }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 样式设置 -->
				<view class="property-section" v-if="selectedElement.type === 'text'">
					<view class="section-header">
						<uv-icon name="brush" size="16" color="#8B1538"></uv-icon>
						<text>样式设置</text>
					</view>
					
					<!-- 文字样式 -->
					<view class="property-item">
						<view class="property-label">
							<uv-icon name="text" size="14" color="#666"></uv-icon>
							<text>字形</text>
						</view>
						<view class="style-buttons enhanced">
							<view class="style-btn" :class="{active: textStyle.bold}" @click="toggleTextStyle('bold')">
								<text class="style-icon bold">B</text>
							</view>
							<view class="style-btn" :class="{active: textStyle.italic}" @click="toggleTextStyle('italic')">
								<text class="style-icon italic">I</text>
							</view>
							<view class="style-btn" :class="{active: textStyle.underline}" @click="toggleTextStyle('underline')">
								<text class="style-icon underline">U</text>
							</view>
							<view class="style-btn" :class="{active: textStyle.strikethrough}" @click="toggleTextStyle('strikethrough')">
								<text class="style-icon strikethrough">S</text>
							</view>
						</view>
					</view>
					
					<!-- 文字大小 -->
					<view class="property-item">
						<view class="property-label">
							<uv-icon name="resize" size="14" color="#666"></uv-icon>
							<text>字号</text>
						</view>
						<view class="slider-control enhanced">
							<view class="slider-btn" @click="decreaseFontSize">
								<uv-icon name="minus" size="16" color="#8B1538"></uv-icon>
							</view>
							<view class="slider-wrapper">
								<slider class="size-slider" min="12" max="72" :value="fontSize" @change="onFontSizeChange" activeColor="#8B1538" backgroundColor="#f0f0f0" block-size="20" />
								<text class="slider-value">{{ fontSize }}px</text>
							</view>
							<view class="slider-btn" @click="increaseFontSize">
								<uv-icon name="plus" size="16" color="#8B1538"></uv-icon>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 位置设置 -->
				<view class="property-section">
					<view class="section-header">
						<uv-icon name="grid" size="16" color="#8B1538"></uv-icon>
						<text>位置设置</text>
					</view>
					
					<!-- 对齐方式 -->
					<view class="property-item">
						<view class="property-label">
							<uv-icon name="align-left" size="14" color="#666"></uv-icon>
							<text>水平对齐</text>
						</view>
						<view class="align-buttons enhanced">
							<view class="align-btn" :class="{active: horizontalAlign === 'left'}" @click="setHorizontalAlign('left')" title="左对齐">
								<uv-icon name="align-left" size="18"></uv-icon>
							</view>
							<view class="align-btn" :class="{active: horizontalAlign === 'center'}" @click="setHorizontalAlign('center')" title="居中对齐">
								<uv-icon name="align-center" size="18"></uv-icon>
							</view>
							<view class="align-btn" :class="{active: horizontalAlign === 'right'}" @click="setHorizontalAlign('right')" title="右对齐">
								<uv-icon name="align-right" size="18"></uv-icon>
							</view>
						</view>
					</view>
					
					<view class="property-item">
						<view class="property-label">
							<uv-icon name="align-center" size="14" color="#666"></uv-icon>
							<text>垂直对齐</text>
						</view>
						<view class="align-buttons enhanced">
							<view class="align-btn" :class="{active: verticalAlign === 'top'}" @click="setVerticalAlign('top')" title="顶部对齐">
								<uv-icon name="arrow-up" size="18"></uv-icon>
							</view>
							<view class="align-btn" :class="{active: verticalAlign === 'middle'}" @click="setVerticalAlign('middle')" title="垂直居中">
								<uv-icon name="minus" size="18"></uv-icon>
							</view>
							<view class="align-btn" :class="{active: verticalAlign === 'bottom'}" @click="setVerticalAlign('bottom')" title="底部对齐">
								<uv-icon name="arrow-down" size="18"></uv-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 保存按钮 -->
		<view class="save-area">
			<view class="save-button" @click="saveTemplate">
				<text class="save-text">保 存</text>
			</view>
		</view>
		
		<!-- 模板文字选择弹窗 -->
		<u-popup :show="showTemplateTextPopup" mode="center" @close="closeTemplateTextPopup" :round="10" :closeable="true" :safeAreaInsetBottom="false" :overlay="true" :closeOnClickOverlay="true">
			<view class="template-text-popup-content"> 
				<view class="template-fields">
					<view class="template-field-button" 
				      v-for="field in availableTemplateFields" 
				      :key="field" 
				      :class="{active: selectedTemplateField === field}"
				      @click="selectTemplateField(field)">
					<text>{{ field }}</text>
				</view>
				</view>
				<view class="popup-actions">
					<view class="popup-action-button cancel" @click="closeTemplateTextPopup">
						<text>取消</text>
					</view>
					<view class="popup-action-button confirm" @click="insertTemplateField">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>


	</view>
</template>
<script>
	// uv-popup 组件通过 uni_modules 自动注册，无需手动导入
	export default {
		components: {
			// uv-ui 组件会自动注册，无需手动导入
		},
		data() {
			return {
				templateId: null,
				templateName: '',
				backgroundImage: '',
				backgroundColor: '#FFFFFF',
	
				selectedElement: null,
				horizontalAlign: 'center', // 默认水平对齐
				verticalAlign: 'middle', // 默认垂直对齐
				textStyle: {
					bold: false,
					italic: false,
					underline: false,
					strikethrough: false
				},
				heightScale: 1,
				fontSize: 24,
				elements: [],
				ctx: null,
				canvasWidth: 0,
				canvasHeight: 0,
				dragging: false,
				dragStartX: 0,
				dragStartY: 0,
				elementStartX: 0,
				elementStartY: 0,
				// 模板文字选择弹窗相关
				showTemplateTextPopup: false,
				availableTemplateFields: ['姓名', '职称', '工号', '公司名称'],
				selectedTemplateField: '',
				currentEditingText: null, // 当前正在编辑的文本元素
				// 字体选择相关
				showFontSelector: false,
				availableFonts: [
					{ name: '黑体', value: 'SimHei' },
					{ name: '楷体', value: 'KaiTi' },
					{ name: '仿宋', value: 'FangSong' },
					{ name: '宋体', value: 'SimSun' }
				],
				selectedFont: { name: '黑体', value: 'SimHei' },
				// 字体颜色选择相关
				showColorSelector: false,
				availableColors: [
					{ name: '红色', value: '#FF0000' },
					{ name: '白色', value: '#FFFFFF' },
					{ name: '黑色', value: '#000000' },
					{ name: '黄色', value: '#FFFF00' },
					{ name: '橙色', value: '#FFA500' },
					{ name: '绿色', value: '#008000' },
					{ name: '蓝色', value: '#0000FF' }
				],
				selectedColor: { name: '黑色', value: '#000000' },
				// 内部对齐选择相关
				showInternalAlignSelector: false,
				availableInternalAligns: [
					{ name: '左对齐', value: 'left' },
					{ name: '居中', value: 'center' },
					{ name: '右对齐', value: 'right' },
					{ name: '两端对齐', value: 'justify' }
				],
				selectedInternalAlign: { name: '居中', value: 'center' },
				// 位置对齐选择相关
				showPositionAlignSelector: false,
				availablePositionAligns: [
					{ name: '左上', value: 'top-left' },
					{ name: '居中上', value: 'top-center' },
					{ name: '右上', value: 'top-right' },
					{ name: '左中', value: 'middle-left' },
					{ name: '居中', value: 'middle-center' },
					{ name: '右中', value: 'middle-right' },
					{ name: '左下', value: 'bottom-left' },
					{ name: '居中下', value: 'bottom-center' },
					{ name: '右下', value: 'bottom-right' }
				],
				selectedPositionAlign: { name: '居中', value: 'middle-center' }
			};
		},
		onLoad(options) {
			if (options.templateId) {
				this.templateId = options.templateId;
				this.loadTemplateData();
			}
		},
		onReady() {
			this.initCanvas();
		},
		methods: {
			initCanvas() {
				this.ctx = uni.createCanvasContext('templateCanvas', this);
				this.getCanvasDimensions().then(dimensions => {
					this.canvasWidth = dimensions.width;
					this.canvasHeight = dimensions.height;
					this.renderCanvas();
				});
			},
			// 打开模板文字选择弹窗
			openTemplateTextPopup() {
				console.log('openTemplateTextPopup called');
				console.log('availableTemplateFields:', this.availableTemplateFields);
				this.showTemplateTextPopup = true;
			},
			// 关闭模板文字选择弹窗
			closeTemplateTextPopup() {
				console.log('closeTemplateTextPopup called');
				this.showTemplateTextPopup = false;
				this.selectedTemplateField = ''; // 重置选择
			},
			// 选择模板字段
			selectTemplateField(field) {
				console.log('selectTemplateField called with:', field);
				console.log('之前选中的字段:', this.selectedTemplateField);
				this.selectedTemplateField = field;
				console.log('现在选中的字段:', this.selectedTemplateField);
				// 强制更新视图
				this.$forceUpdate();
			},
			
			// 插入模板字段到画布中
			insertTemplateField() {
				console.log('insertTemplateField called, selectedTemplateField:', this.selectedTemplateField);
				
				try {
					// 检查是否选择了模板字段
					if (!this.selectedTemplateField) {
						console.error('没有选中的模板字段');
						uni.showToast({
							title: '请选择一个字段',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					
					// 创建新的文本元素并添加到画布
					const templateText = `{{${this.selectedTemplateField}}}`;
					const newTextElement = {
					id: Date.now(), // 使用时间戳作为唯一ID
					type: 'text',
					text: templateText.trim(),
					x: 50, // 默认位置
					y: 50,
					width: 200, // 默认宽度
					height: 40, // 默认高度
					fontSize: 24,
					color: '#000000',
					horizontalAlign: 'center',
					verticalAlign: 'top',
					heightScale: 1, // 默认高度拉伸比例
					textStyle: {
						bold: false,
						italic: false,
						underline: false,
						strikethrough: false
					}
				};
					
					// 添加到元素数组
					this.elements.push(newTextElement);
					
					// 选中新创建的元素
					this.selectedElement = newTextElement;
					this.currentEditingText = newTextElement;
					
					// 重新渲染画布
					this.renderCanvas();
					console.log('模板字段已添加到画布:', templateText);
					
					// 显示成功提示
					uni.showToast({
						title: '模板字段已添加到画布',
						icon: 'success',
						duration: 1500
					});
				} catch (error) {
					console.error('插入模板字段时出错:', error);
					uni.showToast({
						title: '插入失败，请重试',
						icon: 'none',
						duration: 2000
					});
				} finally {
					// 关闭弹窗
					this.showTemplateTextPopup = false;

					// 重置选择
					this.selectedTemplateField = '';
				}
			},
			// 确认模板文字选择并插入
			confirmTemplateTextSelection() {
				if (!this.selectedTemplateField) {
					uni.showToast({
						title: '请选择一个字段',
						icon: 'none'
					});
					return;
				}
				const newTextElement = {
				id: Date.now(),
				type: 'template-text',
				text: this.selectedTemplateField, // 使用选中的字段作为文本
				x: this.canvasWidth / 2 - 50, // 初始位置，可调整
				y: this.canvasHeight / 2 - 15,
				width: 100, // 初始宽度，可调整
				height: 30, // 初始高度，可调整
				color: '#000000',
				fontSize: this.fontSize,
				horizontalAlign: 'center',
				verticalAlign: 'middle',
				heightScale: 1, // 默认高度拉伸比例
				textStyle: { ...this.textStyle }
			};
				this.elements.push(newTextElement);
				this.selectedElement = newTextElement;
				this.renderCanvas();
				this.showTemplateTextPopup = false;
				this.selectedTemplateField = ''; // 重置选择
			},
			getCanvasDimensions() {
				return new Promise(resolve => {
					const query = uni.createSelectorQuery().in(this);
					query.select('.template-canvas').boundingClientRect(data => {
						if (data) {
								resolve({ width: data.width, height: data.height });
							} else {
								resolve({ width: 300, height: 200 }); // 默认回退值
							}
						}).exec();
				});
			},
			renderCanvas() {
				if (!this.ctx) return;
				this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
				// 如果没有背景图片则绘制背景颜色
				if (!this.backgroundImage && this.backgroundColor) {
				    this.ctx.setFillStyle(this.backgroundColor);
				    this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
				}
				this.elements.forEach(element => {
					this.drawElement(element);
				});
				if (this.selectedElement) {
					this.drawSelectionBorder(this.selectedElement);
				}
				this.ctx.draw();
			},
			drawElement(element) {
				if (!this.ctx) return;
				if (element.type.includes('text')) {
				this.ctx.setFillStyle(element.color || '#000000');

				let fontStyle = 'normal';
				if (element.textStyle && element.textStyle.italic) fontStyle = 'italic';
				let fontWeight = 'normal';
				if (element.textStyle && element.textStyle.bold) fontWeight = 'bold';
				const baseFontSize = element.fontSize || 24;
				const heightScale = element.heightScale || 1;
				// 应用高度拉伸到字体大小
				const fontSize = Math.round(baseFontSize * heightScale);
				const fontFamily = element.fontFamily || 'sans-serif';
				
				// 设置字体大小
				this.ctx.setFontSize(fontSize);
				
				// 尝试设置完整的字体样式（在支持的平台上）
				try {
					const fontString = fontStyle + " " + fontWeight + " " + fontSize + "px " + fontFamily;
					if (this.ctx.font !== undefined) {
						this.ctx.font = fontString;
					}
				} catch (e) {
					console.log('Font setting not supported on this platform');
				}

					let textAlign = element.horizontalAlign || 'left';
					this.ctx.setTextAlign(textAlign);

					let textBaseline = element.verticalAlign || 'top'; // 默认为 'top'，以避免 alphabetic 基线问题导致顶部溢出
					this.ctx.setTextBaseline(textBaseline);

					let x = element.x;
					let y = element.y;
					
					// 应用高度拉伸到元素高度
					const scaledHeight = element.height * heightScale;

					// 根据 textAlign 调整 x
					if (textAlign === 'center') x += element.width / 2;
					else if (textAlign === 'right') x += element.width;

					// 根据 textBaseline 调整 y（使用拉伸后的高度）
					if (textBaseline === 'middle') y += scaledHeight / 2;
					else if (textBaseline === 'bottom') y += scaledHeight;
					// 对于 'top'，y 已经是正确的

					// 绘制文本
				this.ctx.fillText(element.text, x, y);
				
				// 如果是粗体且平台不支持font属性，通过多次绘制模拟粗体效果
				if (element.textStyle && element.textStyle.bold) {
					this.ctx.fillText(element.text, x + 0.5, y);
					this.ctx.fillText(element.text, x, y + 0.5);
					this.ctx.fillText(element.text, x + 0.5, y + 0.5);
				}

					// 手动绘制文本装饰（下划线、删除线）
					if (element.textStyle && (element.textStyle.underline || element.textStyle.strikethrough)) {
						const textMetrics = this.ctx.measureText(element.text); // 可能并非所有平台都完全支持或准确
						const textWidth = textMetrics ? textMetrics.width : (element.text.length * fontSize * 0.6); // 回退宽度估算
						
						// 应用高度拉伸到装饰线粗细
						const lineWidth = Math.max(1, Math.round(heightScale));
						this.ctx.setLineWidth(lineWidth); 
						this.ctx.setStrokeStyle(element.color || '#000000');

						let decorationXStart = element.x; // 左对齐时从元素的 x 开始
						if (textAlign === 'center') decorationXStart = element.x + (element.width - textWidth) / 2;
						else if (textAlign === 'right') decorationXStart = element.x + element.width - textWidth;

						if (element.textStyle.underline) {
							let underlineY = y;
							if (textBaseline === 'top') underlineY = element.y + fontSize * 0.9; 
							else if (textBaseline === 'middle') underlineY = element.y + scaledHeight / 2 + fontSize * 0.4;
							else if (textBaseline === 'bottom') underlineY = element.y + scaledHeight - fontSize * 0.1;
							this.ctx.beginPath();
							this.ctx.moveTo(decorationXStart, underlineY);
							this.ctx.lineTo(decorationXStart + textWidth, underlineY);
							this.ctx.stroke();
						}
						if (element.textStyle.strikethrough) {
							let strikeY = y;
							if (textBaseline === 'top') strikeY = element.y + fontSize / 2;
							else if (textBaseline === 'middle') strikeY = element.y + scaledHeight / 2;
							else if (textBaseline === 'bottom') strikeY = element.y + scaledHeight - fontSize / 2;
							this.ctx.beginPath();
							this.ctx.moveTo(decorationXStart, strikeY);
							this.ctx.lineTo(decorationXStart + textWidth, strikeY);
							this.ctx.stroke();
						}
					}
				} else if (element.type === 'image') {
					const heightScale = element.heightScale || 1;
					const scaledHeight = element.height * heightScale;
					this.ctx.drawImage(element.src, element.x, element.y, element.width, scaledHeight);
				}
			},
			drawSelectionBorder(element) {
				if (!this.ctx) return;
				
				// 应用高度拉伸到选择边框
				const heightScale = element.heightScale || 1;
				const scaledHeight = element.height * heightScale;
				
				// 绘制选择边框
				this.ctx.setStrokeStyle('#007AFF'); // 蓝色选择边框
				this.ctx.setLineWidth(2);
				this.ctx.strokeRect(element.x - 2, element.y - 2, element.width + 4, scaledHeight + 4);
				
				// 绘制控制图标
				const iconSize = 20;
				const iconRadius = iconSize / 2;
				
				// 左上角 - 编辑图标
				const editX = element.x - iconRadius;
				const editY = element.y - iconRadius;
				this.drawControlIcon(editX, editY, iconSize, '#4CAF50', '/static/images/edit-icon.svg');
				
				// 右上角 - 删除图标
				const deleteX = element.x + element.width - iconRadius;
				const deleteY = element.y - iconRadius;
				this.drawControlIcon(deleteX, deleteY, iconSize, '#F44336', '/static/images/close.svg');
		},
			
			drawControlIcon(x, y, size, bgColor, imagePath) {
				if (!this.ctx) return;
				
				// 绘制圆形背景
				this.ctx.setFillStyle(bgColor);
				this.ctx.beginPath();
				this.ctx.arc(x + size/2, y + size/2, size/2, 0, 2 * Math.PI);
				this.ctx.fill();
				
				// 绘制白色边框
				this.ctx.setStrokeStyle('#FFFFFF');
				this.ctx.setLineWidth(1);
				this.ctx.beginPath();
				this.ctx.arc(x + size/2, y + size/2, size/2, 0, 2 * Math.PI);
				this.ctx.stroke();
				
				// 绘制图片
				const iconSize = size * 0.6; // 图片大小为圆形的60%
				const iconX = x + (size - iconSize) / 2;
				const iconY = y + (size - iconSize) / 2;
				this.ctx.drawImage(imagePath, iconX, iconY, iconSize, iconSize);
			},
			loadTemplateData() {
				// 从源（例如 API、本地存储）加载模板数据的占位符
				// 目前，我们假设它会填充 this.templateName、this.elements 等。
				console.log('Loading template data for ID:', this.templateId);
				// 示例： 
				// this.templateName = 'Loaded Template';
				// this.elements = [{ type: 'fixed-text', text: 'Hello', x: 50, y: 50, width: 100, height: 30, color: '#FF0000', fontSize: 20 }];
				// this.backgroundImage = 'path/to/image.jpg';
				// this.backgroundColor = '#DDDDDD';
				this.renderCanvas();
			},
			insertTemplateText() {
				// 此方法现在由 openTemplateTextPopup 和 confirmTemplateTextSelection 处理
				// 为安全起见，暂时让它调用新的弹窗方法
				this.openTemplateTextPopup();
			},
			insertFixedText() { 
				// 创建新的固定文字元素
				const newTextElement = {
					id: Date.now(),
					type: 'fixed-text',
					text: '固定文字',
					x: this.canvasWidth / 2 - 50,
					y: this.canvasHeight / 2 - 15,
					width: 100,
					height: 30,
					color: '#000000',
					fontSize: this.fontSize,
					horizontalAlign: 'center',
					verticalAlign: 'middle',
					heightScale: 1,
					textStyle: { ...this.textStyle }
				};
				this.elements.push(newTextElement);
				this.selectedElement = newTextElement;
				this.syncElementProperties();
				this.renderCanvas();
			},
			insertImage() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						const newImageElement = {
						id: Date.now(),
						type: 'image',
						src: res.tempFilePaths[0],
						x: this.canvasWidth / 2 - 50,
						y: this.canvasHeight / 2 - 25,
						width: 100,
						height: 50,
						heightScale: 1, // 默认高度拉伸比例
					};
						this.elements.push(newImageElement);
						this.selectedElement = newImageElement;
						this.renderCanvas();
					}
				});
			},
			selectBackground() {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						this.backgroundImage = res.tempFilePaths[0];
						this.backgroundColor = ''; // 如果设置了图片，则清除颜色
						this.renderCanvas();
					}
				});
			},
			selectSolidColor() {
				// 理想情况下，这会打开一个颜色选择器。为简单起见，循环显示一些颜色或设置默认值。
				// 对于真实的应用程序，请使用颜色选择器组件。
				this.backgroundColor = this.backgroundColor === '#DDDDDD' ? '#AACCFF' : '#DDDDDD';
				this.backgroundImage = ''; // 如果设置了颜色，则清除图片
				this.renderCanvas();
			},
			clearBackground() {
				this.backgroundImage = '';
				this.backgroundColor = '#FFFFFF'; // 默认为白色
				this.renderCanvas();
			},
			saveTemplate() {
				const templateData = {
					id: this.templateId || Date.now(),
					name: this.templateName,
					backgroundImage: this.backgroundImage,
					backgroundColor: this.backgroundColor,
					elements: this.elements
				};
				console.log('Saving template:', templateData);
				// 实际保存逻辑的占位符（例如 API 调用、本地存储）
				uni.showToast({ title: '模板已保存' });
			},
			// 同步选中元素的属性到面板
			syncElementProperties() {
				if (this.selectedElement) {
					// 根据 selectedElement 属性填充属性面板字段
					if (this.selectedElement.type && this.selectedElement.type.includes('text')) {
						this.horizontalAlign = this.selectedElement.horizontalAlign || 'center';
						this.verticalAlign = this.selectedElement.verticalAlign || 'middle';
						this.textStyle = { ... (this.selectedElement.textStyle || { bold: false, italic: false, underline: false, strikethrough: false }) };
						this.fontSize = this.selectedElement.fontSize || 24;
						// 同步字体信息
					const elementFontFamily = this.selectedElement.fontFamily || 'SimHei';
					const matchedFont = this.availableFonts.find(font => font.value === elementFontFamily);
					this.selectedFont = matchedFont || this.availableFonts[0];
					// 同步颜色信息
					const elementColor = this.selectedElement.color || '#000000';
					const matchedColor = this.availableColors.find(color => color.value === elementColor);
					this.selectedColor = matchedColor || this.availableColors[2]; // 默认黑色
					// 同步内部对齐信息
					const elementTextAlign = this.selectedElement.textAlign || this.selectedElement.horizontalAlign || 'center';
					const matchedInternalAlign = this.availableInternalAligns.find(align => align.value === elementTextAlign);
					this.selectedInternalAlign = matchedInternalAlign || this.availableInternalAligns[1]; // 默认居中
					// 同步位置对齐信息
					const elementVerticalAlign = this.selectedElement.verticalAlign || 'middle';
					const elementHorizontalAlign = this.selectedElement.horizontalAlign || 'center';
					const positionAlignValue = elementVerticalAlign + "-" + elementHorizontalAlign;
					const matchedPositionAlign = this.availablePositionAligns.find(align => align.value === positionAlignValue);
					this.selectedPositionAlign = matchedPositionAlign || this.availablePositionAligns[4]; // 默认居中
					}
					this.heightScale = this.selectedElement.heightScale || 1;
				}
			},
			// 应用属性更改到选中元素
			applyPropertyChanges() {
				if (this.selectedElement) {
					// 重新渲染画布以应用更改
					this.renderCanvas();
				}
			},
			// 设置水平对齐方式
			setHorizontalAlign(align) {
				this.horizontalAlign = align;
				if (this.selectedElement) {
					this.selectedElement.horizontalAlign = align;
					this.applyPropertyChanges();
				}
			},
			// 设置垂直对齐方式
			setVerticalAlign(align) {
				this.verticalAlign = align;
				if (this.selectedElement) {
					this.selectedElement.verticalAlign = align;
					this.applyPropertyChanges();
				}
			},
			// 切换文字样式
			toggleTextStyle(style) {
				this.textStyle[style] = !this.textStyle[style];
				if (this.selectedElement) {
					// 确保textStyle对象存在
					if (!this.selectedElement.textStyle) {
						this.selectedElement.textStyle = {
							bold: false,
							italic: false,
							underline: false,
							strikethrough: false
						};
					}
					this.selectedElement.textStyle[style] = this.textStyle[style];
					this.applyPropertyChanges();
				}
			},
			// 切换字体选择器显示
			toggleFontSelector() {
				this.showFontSelector = !this.showFontSelector;
			},
			// 选择字体
			selectFont(font) {
				this.selectedFont = font;
				this.showFontSelector = false;
				if (this.selectedElement && this.selectedElement.type && this.selectedElement.type.includes('text')) {
					this.selectedElement.fontFamily = font.value;
					this.applyPropertyChanges();
				}
			},
			// 切换颜色选择器显示
			toggleColorSelector() {
				this.showColorSelector = !this.showColorSelector;
			},
			// 选择颜色
			selectColor(color) {
				this.selectedColor = color;
				this.showColorSelector = false;
				if (this.selectedElement && this.selectedElement.type && this.selectedElement.type.includes('text')) {
					this.selectedElement.color = color.value;
					this.applyPropertyChanges();
				}
			},
			// 切换内部对齐选择器显示
			toggleInternalAlignSelector() {
				this.showInternalAlignSelector = !this.showInternalAlignSelector;
			},
			// 选择内部对齐
			selectInternalAlign(align) {
				this.selectedInternalAlign = align;
				this.showInternalAlignSelector = false;
				if (this.selectedElement && this.selectedElement.type && this.selectedElement.type.includes('text')) {
					this.selectedElement.textAlign = align.value;
					this.selectedElement.horizontalAlign = align.value;
					this.applyPropertyChanges();
				}
			},
			// 切换位置对齐选择器显示
			togglePositionAlignSelector() {
				this.showPositionAlignSelector = !this.showPositionAlignSelector;
			},
			// 选择位置对齐
			selectPositionAlign(align) {
				this.selectedPositionAlign = align;
				this.showPositionAlignSelector = false;
				if (this.selectedElement && this.selectedElement.type && this.selectedElement.type.includes('text')) {
					// 解析位置对齐值
					const [vertical, horizontal] = align.value.split('-');
					
					// 基于红色画布范围（整个画布）进行位置对齐
					// 垂直位置调整
					if (vertical === 'top') {
						this.selectedElement.y = 10; // 距离画布顶部10px
						this.selectedElement.verticalAlign = 'top';
					} else if (vertical === 'middle') {
						this.selectedElement.y = (this.canvasHeight - this.selectedElement.height) / 2;
						this.selectedElement.verticalAlign = 'middle';
					} else if (vertical === 'bottom') {
						this.selectedElement.y = this.canvasHeight - this.selectedElement.height - 10; // 距离画布底部10px
						this.selectedElement.verticalAlign = 'bottom';
					}
					
					// 水平位置调整
					if (horizontal === 'left') {
						this.selectedElement.x = 10; // 距离画布左边10px
						this.selectedElement.horizontalAlign = 'left';
					} else if (horizontal === 'center') {
						this.selectedElement.x = (this.canvasWidth - this.selectedElement.width) / 2;
						this.selectedElement.horizontalAlign = 'center';
					} else if (horizontal === 'right') {
						this.selectedElement.x = this.canvasWidth - this.selectedElement.width - 10; // 距离画布右边10px
						this.selectedElement.horizontalAlign = 'right';
					}
					
					this.renderCanvas();
				}
			},
			// 高度变化
			onHeightChange(e) {
				let newScale = parseFloat(e.detail.value);
				// 确保值在有效范围内
				newScale = Math.max(0.2, Math.min(3, newScale));
				this.heightScale = parseFloat(newScale.toFixed(1));
				
				if (this.selectedElement) {
					// 应用高度拉伸到选中元素
					// 对于文本元素，影响字体大小和行高
					// 对于图片元素，影响图片显示高度
					this.selectedElement.heightScale = this.heightScale;
					this.renderCanvas();
				}
			},
			// 增加高度
			increaseHeight() {
				if (this.heightScale < 3) { // 扩大最大范围到3倍
					this.heightScale = parseFloat((this.heightScale + 0.1).toFixed(1));
					if (this.selectedElement) {
						this.selectedElement.heightScale = this.heightScale;
						this.renderCanvas();
					}
				}
			},
			// 减少高度
			decreaseHeight() {
				if (this.heightScale > 0.2) { // 提高最小值到0.2，避免过小
					this.heightScale = parseFloat((this.heightScale - 0.1).toFixed(1));
					if (this.selectedElement) {
						this.selectedElement.heightScale = this.heightScale;
						this.renderCanvas();
					}
				}
			},
			// 字体大小变化
			onFontSizeChange(e) {
				this.fontSize = parseInt(e.detail.value, 10);
				if (this.selectedElement && this.selectedElement.type.includes('text')) {
					this.selectedElement.fontSize = this.fontSize;
					this.renderCanvas();
				}
			},
			// 增加字体大小
			increaseFontSize() {
				if (this.fontSize < 72) {
					this.fontSize += 1;
					if (this.selectedElement && this.selectedElement.type.includes('text')) {
						this.selectedElement.fontSize = this.fontSize;
						this.renderCanvas();
					}
				}
			},
			// 减少字体大小
			decreaseFontSize() {
				if (this.fontSize > 12) {
					this.fontSize -= 1;
					if (this.selectedElement && this.selectedElement.type.includes('text')) {
						this.selectedElement.fontSize = this.fontSize;
						this.renderCanvas();
					}
				}
			},
			ensureElementInBounds(element, canvasDimensions) {
				// 确保坐标和尺寸是数字，如果不是则默认为0，以增加稳健性
				element.x = Number(element.x) || 0;
				element.y = Number(element.y) || 0;
				element.width = Number(element.width) || 0;
				element.height = Number(element.height) || 0;

				// 水平边界处理
				// 首先检查是否超出右边界并调整
				if (element.x + element.width > canvasDimensions.width) {
					element.x = canvasDimensions.width - element.width;
				}
				// 然后检查调整后（或原本就）是否超出左边界
				if (element.x < 0) {
					element.x = 0;
				}

				// 垂直边界处理
				// 首先检查是否超出下边界并调整
				if (element.y + element.height > canvasDimensions.height) {
					element.y = canvasDimensions.height - element.height;
				}
				// 然后检查调整后（或原本就）是否超出上边界
				if (element.y < 0) {
					element.y = 0;
				}
			},
			canvasTouchStart(event) {
				const touch = event.touches[0];
				const touchX = touch.x;
				const touchY = touch.y;
				console.log('Canvas touch start at:', touchX, touchY);
				
				// 检查是否点击了已选中元素的控制图标
				if (this.selectedElement) {
					console.log('Selected element exists:', this.selectedElement);
					const iconSize = 20;
					const iconRadius = iconSize / 2;
					const el = this.selectedElement;
					
					// 左上角 - 编辑图标
				const editX = el.x - iconRadius;
				const editY = el.y - iconRadius;
				const editCenterX = editX + iconRadius;
				const editCenterY = editY + iconRadius;
				console.log('Edit icon center:', editCenterX, editCenterY, 'radius:', iconRadius);
				console.log('Touch point:', touchX, touchY);
				const isInEditIcon = this.isPointInCircle(touchX, touchY, editCenterX, editCenterY, iconRadius);
				console.log('Is in edit icon:', isInEditIcon);
				if (isInEditIcon) {
					console.log('点击了编辑图标，同步属性到面板');
					// 同步属性到面板
					this.currentEditingText = el.type && el.type.includes('text') ? el : null;
					console.log('Current editing text:', this.currentEditingText);
					this.syncElementProperties();
					return;
				}
					
					// 右上角 - 删除图标
					const deleteX = el.x + el.width - iconRadius;
					const deleteY = el.y - iconRadius;
					if (this.isPointInCircle(touchX, touchY, deleteX + iconRadius, deleteY + iconRadius, iconRadius)) {
						console.log('点击了删除图标');
						// 删除当前选中的元素
						const index = this.elements.findIndex(item => item === this.selectedElement);
						if (index !== -1) {
							this.elements.splice(index, 1);
							this.selectedElement = null;
							this.currentEditingText = null;
							this.renderCanvas();
					}
					return;
				}
				 
			}

			let clickedElement = null;
			// 反向迭代以选择最顶层的元素
			for (let i = this.elements.length - 1; i >= 0; i--) {
				const el = this.elements[i];
				if (touchX >= el.x && touchX <= el.x + el.width &&
					touchY >= el.y && touchY <= el.y + el.height) {
					clickedElement = el;
					break;
				}
			}

			if (clickedElement) {
			console.log('Element clicked:', clickedElement);
			this.selectedElement = clickedElement;
			// 如果选中的是文本元素，设置为当前编辑的文本
			if (clickedElement.type && clickedElement.type.includes('text')) {
				this.currentEditingText = clickedElement;
				console.log('设置当前编辑文本:', this.currentEditingText);
			} else {
				this.currentEditingText = null;
			}
			this.dragging = true;
			this.dragStartX = touchX;
			this.dragStartY = touchY;
			this.elementStartX = clickedElement.x;
			this.elementStartY = clickedElement.y;
			console.log('同步选中元素属性到面板');
			this.syncElementProperties(); // 同步选中元素属性到面板
		} else {
			console.log('No element clicked, clearing selection');
			this.selectedElement = null;
			this.currentEditingText = null;
			this.dragging = false;
		}
				this.renderCanvas();
			},
			
			isPointInCircle(x, y, centerX, centerY, radius) {
				const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
				return distance <= radius;
			},
			canvasTouchMove(event) {
				if (!this.dragging || !this.selectedElement) return;
				event.preventDefault(); // 拖动时阻止页面滚动

				const touch = event.touches[0];
				const deltaX = touch.x - this.dragStartX;
				const deltaY = touch.y - this.dragStartY;

				this.selectedElement.x = this.elementStartX + deltaX;
				this.selectedElement.y = this.elementStartY + deltaY;

				this.getCanvasDimensions().then(canvasDimensions => {
					this.ensureElementInBounds(this.selectedElement, canvasDimensions);
					this.renderCanvas();
				});
			},
			canvasTouchEnd() {
				this.dragging = false;
			},
		}
	}
</script>

<style lang="scss" scoped>
.template-text-popup-content {
	padding: 40rpx;
	background-color: #fff;
	width: 600rpx;
	box-sizing: border-box;
	border-radius: 20rpx;
	position: relative;
	z-index: 9999;
	max-height: 66.67vh; /* 限制为屏幕高度的2/3 */
	overflow-y: auto;
	.popup-title {
		font-size: 36rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 20rpx;
	}
	.popup-subtitle {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
	}
	.template-fields {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-around;
		margin-bottom: 40rpx;
		.template-field-button {
			padding: 16rpx 24rpx;
			border: 2rpx solid #ccc;
			border-radius: 8rpx;
			margin: 10rpx;
			text-align: center;
			min-width: 160rpx;
			box-sizing: border-box;
			background-color: #fff;
			&.active {
				background-color: #007aff;
				color: white;
				border-color: #007aff;
			}
		}
	}
	.popup-actions {
		display: flex;
		justify-content: space-between;
		.popup-action-button {
			flex: 1;
			padding: 20rpx 0;
			text-align: center;
			border-radius: 8rpx;
			font-size: 32rpx;
			&.cancel {
				background-color: #f0f0f0;
				color: #333;
				margin-right: 20rpx;
			}
			&.confirm {
				background-color: #007aff;
				color: white;
			}
		}
	}
}

/* 主容器样式 */
.edit-container {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	position: relative;
}

/* Canvas画布区域 */
.canvas-area {
	width: 100%;
	height: 33.33vh;
	position: relative;
	background-color: #F5F5F5;
	border-bottom: 1px solid #E0E0E0;
}

.canvas {
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
	touch-action: none;
	background-color: #F5F5F5;
	background-size: cover;
	background-position: center;
}

/* 模板名称区域 */
.template-name-area {
	display: flex;
	align-items: center;
	padding: 15px 20px;
	background-color: #fff;
	border-bottom: 1px solid #f0f0f0;
}

.template-label {
	font-size: 14px;
	color: #666;
	margin-right: 10px;
	flex-shrink: 0;
}

.template-name-input {
	flex: 1;
	padding: 8px 12px;
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	background-color: #fff;
	color: #606266;
	font-size: 14px;
}

/* 分隔线 */
.divider {
	height: 1px;
	background-color: #F0F0F0;
}

/* 区域标题 */
.section-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin: 20px 0 10px 0;
	padding: 0 20px;
}

/* 按钮组 */
.button-group {
	display: flex;
	gap: 10px;
	margin-bottom: 15px;
	justify-content: space-between;
	padding: 0 20px;
}

.action-btn {
	width: 120px;
	height: 36px;
	border: 1px solid #DCDFE6;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	color: #606266;
	background-color: #fff;
	cursor: pointer;
}

.action-btn.primary {
	background-color: #8B1538;
	color: #fff;
	border-color: #8B1538;
}

.action-btn:hover {
	background-color: #f5f7fa;
}

.action-btn.primary:hover {
	background-color: #7A1230;
}

/* 属性设置区域样式 */
.property-area {
	height: 66.67vh;
	padding: 0;
	overflow-y: auto;
	background: linear-gradient(135deg, #ffffff 0%, #f8fafb 100%);
	border-radius: 16px 16px 0 0;
	box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.property-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 20px 15px;
	border-bottom: 1px solid #f0f0f0;
	background: linear-gradient(135deg, #8B1538 0%, #A91B47 100%);
	border-radius: 16px 16px 0 0;
	margin-bottom: 0;
}

.property-title {
	font-size: 18px;
	font-weight: 700;
	color: #fff;
	margin: 0;
}

.element-type-badge {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 6px 12px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20px;
	font-size: 12px;
	color: #fff;
	font-weight: 500;
	backdrop-filter: blur(10px);
}

.element-type-badge.text {
	background: rgba(255, 255, 255, 0.25);
}

.element-type-badge.image {
	background: rgba(255, 255, 255, 0.25);
}

.template-name {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 20rpx;
}

.name-label {
	font-size: 28rpx;
	color: #333333;
	margin-right: 20rpx;
}

.name-input {
	flex: 1;
	height: 70rpx;
	border: 1rpx solid #eeeeee;
	border-radius: 6rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}

.section-title {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 20rpx;
	display: block; /* 确保在按钮之前占据全部宽度 */
}

.insert-buttons, .bg-buttons {
	display: flex;
	flex-direction: row;
	justify-content: space-around; /* 均匀分布按钮 */
	margin-bottom: 30rpx;
}

.insert-button, .bg-button {
	/* 如果希望它们根据内容或固定宽度调整大小，请删除 flex:1 */
	padding: 15rpx 20rpx;
	height: auto; /* 根据内容自动调整高度 */
	min-height: 80rpx; /* 最小高度 */
	background-color: #8B0000; /* 暗红色 */
	color: #ffffff;
	border-radius: 6rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	text-align: center;
	margin: 0 10rpx; /* 在按钮之间添加一些边距 */
	font-size: 26rpx;
}

.insert-button:first-child, .bg-button:first-child {
    margin-left: 0;
}
.insert-button:last-child, .bg-button:last-child {
    margin-right: 0;
}


/* 保存按钮 */
.save-button {
	margin-top: 30px;
	height: 50px;
	background: linear-gradient(135deg, #8B1538 0%, #A91B47 100%);
	border-radius: 25px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	cursor: pointer;
}

.save-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><path d="M0,10 Q25,0 50,10 T100,10 L100,20 L0,20 Z" fill="%23ffffff" opacity="0.1"/></svg>') repeat-x;
	animation: brush-stroke 2s ease-in-out infinite;
}

@keyframes brush-stroke {
	0% { left: -100%; }
	100% { left: 100%; }
}

.save-text {
	font-size: 18px;
	font-weight: bold;
	color: #fff;
	letter-spacing: 2px;
	z-index: 1;
	position: relative;
}



.property-content {
	flex: 1;
	overflow-y: auto;
	min-height: 0;
	max-height: calc(80vh - 200rpx);
	padding-bottom: 120rpx;
}

.property-form {
	padding: 30rpx;
}

/* 属性分组 */
.property-group {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
}

.property-group-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #8B0000;
	margin-bottom: 20rpx;
	padding-bottom: 10rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

/* 属性面板 */
.property-panel {
	padding: 20px;
	max-height: calc(66.67vh - 80px);
	overflow-y: auto;
}

/* 属性分组 */
.property-section {
	margin-bottom: 24px;
	background: #fff;
	border-radius: 12px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	border: 1px solid #f0f0f0;
}

.property-section:last-child {
	margin-bottom: 0;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 16px;
	padding-bottom: 8px;
	border-bottom: 1px solid #f5f5f5;
}

.property-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16px;
	min-height: 44px;
	padding: 0;
	transition: all 0.3s ease;
}

.property-item:last-child {
	margin-bottom: 0;
}

.property-label {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	color: #333;
	font-weight: 500;
	min-width: 80px;
	flex-shrink: 0;
}

.property-input.enhanced {
	flex: 1;
	height: 36px;
	padding: 0 12px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	font-size: 14px;
	background: #fff;
	transition: all 0.3s ease;
}

.property-input.enhanced:focus {
	border-color: #8B1538;
	box-shadow: 0 0 0 2px rgba(139, 21, 56, 0.1);
	outline: none;
}

.property-value {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: 28rpx;
	color: #666666;
}

/* 选择器样式优化 */
.property-selector.enhanced {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8px 12px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	background: #fff;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	min-height: 36px;
}

.property-selector.enhanced:hover {
	border-color: #8B1538;
	box-shadow: 0 2px 8px rgba(139, 21, 56, 0.1);
}

.selector-text {
	font-size: 14px;
	color: #333;
	flex: 1;
}

.property-selector.enhanced .uv-icon.rotate {
	transform: rotate(180deg);
}

.selector-options {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	z-index: 1000;
	background: #fff;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
	margin-top: 4px;
	max-height: 200px;
	overflow-y: auto;
	animation: slideDown 0.2s ease;
}

@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateY(-8px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.option-item {
	padding: 12px 16px;
	border-bottom: 1px solid #f0f0f0;
	cursor: pointer;
	transition: all 0.2s ease;
	font-size: 14px;
	color: #333;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.option-item:last-child {
	border-bottom: none;
}

.option-item:hover {
	background: #f8f9fa;
}

.option-item.active {
	background: #8B1538;
	color: #fff;
}

/* 字体选择器样式 */
.property-label .uv-icon.rotate {
	transform: rotate(180deg);
}

.font-options {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	z-index: 1000;
	background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12), 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
	margin-top: 8rpx;
	max-height: 320rpx;
	overflow-y: auto;
	backdrop-filter: blur(10rpx);
	animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateY(-10rpx) scale(0.95);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

.font-option {
	padding: 18rpx 24rpx;
	border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	font-size: 28rpx;
	color: #333333;
	position: relative;
	overflow: hidden;
}

.font-option::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 0;
	height: 100%;
	background: linear-gradient(90deg, rgba(139, 0, 0, 0.1), rgba(139, 0, 0, 0.05));
	transition: width 0.3s ease;
}

.font-option:last-child {
	border-bottom: none;
}

.font-option:hover {
	background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
	transform: translateX(4rpx);
}

.font-option:hover::before {
	width: 100%;
}

.font-option.active {
	background: linear-gradient(135deg, #8B0000 0%, #a31515 100%);
	color: #ffffff;
	box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
	font-weight: 600;
}

.font-option.active:hover {
	background: linear-gradient(135deg, #a31515 0%, #b71c1c 100%);
	transform: translateX(0);
}

/* 颜色选择器样式 */
.color-display {
	display: flex;
	align-items: center;
	gap: 8px;
}

.color-preview.enhanced {
	width: 20px;
	height: 20px;
	border-radius: 4px;
	border: 2px solid #fff;
	box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
	flex-shrink: 0;
	transition: all 0.2s ease;
}

.color-options {
	padding: 12px;
}

.color-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 8px;
}

.color-item.enhanced {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 6px;
	padding: 8px;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.2s ease;
	position: relative;
}

.color-item.enhanced:hover {
	background: #f8f9fa;
	transform: translateY(-2px);
}

.color-item.enhanced.active {
	background: #f0f8ff;
	transform: translateY(-2px);
}

.color-swatch {
	width: 32px;
	height: 32px;
	border-radius: 6px;
	border: 2px solid #fff;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 0 0 1px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.color-item.enhanced:hover .color-swatch {
	transform: scale(1.1);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.color-name {
	font-size: 12px;
	color: #666;
	text-align: center;
	line-height: 1.2;
}

.color-options {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	z-index: 1000;
	background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12), 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
	margin-top: 8rpx;
	max-height: 320rpx;
	overflow-y: auto;
	backdrop-filter: blur(10rpx);
	animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.color-option {
	padding: 18rpx 24rpx;
	border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	font-size: 28rpx;
	color: #333333;
	display: flex;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;
}

.color-option::before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 0;
	height: 100%;
	background: linear-gradient(90deg, rgba(139, 0, 0, 0.1), rgba(139, 0, 0, 0.05));
	transition: width 0.3s ease;
}

.color-option:last-child {
	border-bottom: none;
}

.color-option:hover {
	background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
	transform: translateX(4rpx);
}

.color-option:hover::before {
	width: 100%;
}

.color-option.active {
	background: linear-gradient(135deg, #8B0000 0%, #a31515 100%);
	color: #ffffff;
	box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
	font-weight: 600;
}

.color-option.active:hover {
	background: linear-gradient(135deg, #a31515 0%, #b71c1c 100%);
	transform: translateX(0);
}

.color-option .color-preview {
	width: 32rpx;
	height: 32rpx;
	border-radius: 6rpx;
	border: 2rpx solid #ffffff;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.12), inset 0 0 0 1rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.color-option:hover .color-preview {
	transform: scale(1.1);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2), inset 0 0 0 1rpx rgba(0, 0, 0, 0.1);
}

.color-option.active .color-preview {
	border-color: rgba(255, 255, 255, 0.8);
	box-shadow: 0 0 0 2rpx rgba(255, 255, 255, 0.5), 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

/* 确保选择器容器有相对定位 */
.property-item {
	position: relative;
}

/* 样式按钮 */
.style-buttons.enhanced {
	display: flex;
	gap: 8px;
	flex: 1;
	justify-content: flex-end;
}

.style-btn {
	width: 36px;
	height: 36px;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #fff;
	transition: all 0.2s ease;
	cursor: pointer;
}

.style-btn:hover {
	border-color: #8B1538;
	background: #f8f9fa;
}

.style-btn.active {
	background: #8B1538;
	border-color: #8B1538;
	color: #fff;
}

.style-icon {
	font-size: 16px;
	font-weight: bold;
	color: inherit;
	font-family: 'Arial', sans-serif;
}

.style-icon.italic {
	font-style: italic;
}

.style-icon.underline {
	text-decoration: underline;
}

.style-icon.strikethrough {
	text-decoration: line-through;
}

/* 对齐按钮 */
.align-buttons.enhanced {
	display: flex;
	gap: 8px;
	flex: 1;
	justify-content: flex-end;
}

.align-btn {
	width: 36px;
	height: 36px;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #fff;
	transition: all 0.2s ease;
	cursor: pointer;
}

.align-btn:hover {
	border-color: #8B1538;
	background: #f8f9fa;
}

.align-btn.active {
	background: #8B1538;
	border-color: #8B1538;
	color: #fff;
}

.align-option::before, .text-style-option::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(circle at center, rgba(139, 0, 0, 0.1) 0%, transparent 70%);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.align-option:hover, .text-style-option:hover {
	border-color: #8B0000;
	background: linear-gradient(135deg, #ffffff 0%, #f0f2f5 100%);
	box-shadow: 0 8rpx 20rpx rgba(139, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
	transform: translateY(-2rpx) scale(1.05);
}

.align-option:hover::before, .text-style-option:hover::before {
	opacity: 1;
}

.align-option.active, .text-style-option.active {
	background: linear-gradient(135deg, #8B0000 0%, #a31515 100%);
	border-color: #8B0000;
	color: #ffffff;
	box-shadow: 0 6rpx 16rpx rgba(139, 0, 0, 0.3), inset 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
	transform: scale(0.95);
}

.align-option:active, .text-style-option:active {
	transform: scale(0.9);
	transition: transform 0.1s ease;
}

.align-option:hover, .text-style-option:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
	border-color: #8B0000;
}

.align-option.active, .text-style-option.active {
	background: linear-gradient(135deg, #8B0000 0%, #a31515 100%);
	border-color: #8B0000;
	color: #ffffff;
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 20rpx rgba(139, 0, 0, 0.3);
}

.align-option.active .uv-icon, .text-style-option.active .uv-icon {
	color: #ffffff;
}

/* 文字样式图标 */
.text-style-icon {
	font-size: 32rpx;
	font-weight: bold;
	color: #666666;
	transition: all 0.2s ease;
	font-family: 'Arial', sans-serif;
}

.text-style-icon.italic {
	font-style: italic;
}

.text-style-icon.underline {
	text-decoration: underline;
}

.text-style-icon.strikethrough {
	text-decoration: line-through;
}

.text-style-option.active .text-style-icon {
	color: #ffffff;
}

/* 滑块控件 */
.slider-control.enhanced {
	display: flex;
	align-items: center;
	gap: 12px;
	flex: 1;
	justify-content: flex-end;
}

.slider-btn {
	width: 32px;
	height: 32px;
	border-radius: 6px;
	background: #fff;
	border: 1px solid #e0e0e0;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	transition: all 0.2s ease;
	flex-shrink: 0;
}

.slider-btn:hover {
	border-color: #8B1538;
	background: #f8f9fa;
}

.slider-btn:active {
	transform: scale(0.95);
}

.slider-wrapper {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
	min-width: 120px;
}

.size-slider {
	width: 100%;
	height: 4px;
	margin: 0;
}

.slider-value {
	font-size: 12px;
	color: #666;
	text-align: center;
	line-height: 1;
}

/* 内部对齐选项样式 */
.internal-align-options {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border: 2rpx solid #e9ecef;
	border-radius: 16rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	z-index: 1000;
	max-height: 400rpx;
	overflow-y: auto;
	backdrop-filter: blur(10rpx);
	animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.internal-align-option {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f1f3f4;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.internal-align-option::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(139, 0, 0, 0.1), transparent);
	transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.internal-align-option:hover::before {
	left: 100%;
}

.internal-align-option:hover {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	transform: translateX(8rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.internal-align-option.active {
	background: linear-gradient(135deg, #8B0000 0%, #A52A2A 50%, #8B0000 100%);
	color: #ffffff;
	border-color: #8B0000;
	box-shadow: 0 6rpx 16rpx rgba(139, 0, 0, 0.3), inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
	transform: translateX(0);
}

.internal-align-option:last-child {
	border-bottom: none;
}

/* 位置对齐选项样式 */
.position-align-options {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border: 2rpx solid #e9ecef;
	border-radius: 16rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	z-index: 1000;
	max-height: 400rpx;
	overflow-y: auto;
	backdrop-filter: blur(10rpx);
	animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.position-align-option {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f1f3f4;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.position-align-option::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(139, 0, 0, 0.1), transparent);
	transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.position-align-option:hover::before {
	left: 100%;
}

.position-align-option:hover {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	transform: translateX(8rpx);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.position-align-option.active {
	background: linear-gradient(135deg, #8B0000 0%, #A52A2A 50%, #8B0000 100%);
	color: #ffffff;
	border-color: #8B0000;
	box-shadow: 0 6rpx 16rpx rgba(139, 0, 0, 0.3), inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
	transform: translateX(0);
}

.position-align-option:last-child {
	border-bottom: none;
}





</style>