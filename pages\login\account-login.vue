<template>
	<view class="container">
		<view class="logo-container">
			<image src="http://*************:8890/i/2025/06/12/logo.png" class="logo" mode="aspectFit"></image>
		</view>
		
		<view class="form-container">
			<vol-form :formFields="formData" :formOptions="formOptions" :labelWidth="0" padding="0">
			</vol-form>
			
			<view class="login-btn-container">
				<view class="login-btn" @click="login">
					<text class="login-text">登 录</text>
				</view>
			</view>
			
			<view class="forgot-password" @click="forgotPassword">
				<text class="forgot-text">忘记密码?</text>
			</view>
		</view>
		
		<view class="register-container">
			<text class="register-text">还没有账号，现在去 </text>
			<text class="register-link" @click="goRegister">免费注册</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					phone: '',
					password: ''
				},
				formOptions: [
					{
						field: 'phone',
						title: '手机号',
						type: 'text',
						placeholder: '请输入手机号'
					},
					{
						field: 'password',
						title: '密码',
						type: 'password',
						placeholder: '请输入密码'
					}
				]
			}
		},
		methods: {
			login() {
				// 登录逻辑
				console.log('登录', this.formData.phone, this.formData.password);
			},
			forgotPassword() {
				// 跳转到忘记密码页面
				uni.navigateTo({
					url: '/pages/login/forget-password'
				});
			},
			goRegister() {
				// 跳转到注册页面
				uni.navigateTo({
					url: '/pages/login/register'
				});
			}
		}
	}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	background-image: url('http://*************:8890/i/2025/06/12/login-bg.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}

.logo-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 80px;
	margin-bottom: 60px;
}

.logo {
	width: 120px;
	height: 120px;
}

.form-container {
	flex: 1;
	width: 100%;
	max-width: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.login-btn-container {
	width: 100%;
	margin-top: 40px;
	margin-bottom: 30px;
}

.login-btn {
	width: 100%;
	height: 50px;
	background: linear-gradient(135deg, #000000, #8B0000, #DC143C);
	border-radius: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	clip-path: polygon(5% 0%, 95% 0%, 98% 20%, 100% 50%, 98% 80%, 95% 100%, 5% 100%, 2% 80%, 0% 50%, 2% 20%);
	box-shadow: inset 0 2px 4px rgba(0,0,0,0.3), 0 4px 8px rgba(0,0,0,0.2);
	border: 1px solid rgba(139, 0, 0, 0.8);
}

.login-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(220,20,60,0.4), transparent);
	transition: left 0.8s ease-in-out;
}

.login-btn:active::before {
	left: 100%;
}

.login-btn::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(ellipse at 20% 30%, rgba(139,0,0,0.6) 0%, transparent 50%),
	            radial-gradient(ellipse at 80% 70%, rgba(220,20,60,0.4) 0%, transparent 40%),
	            radial-gradient(ellipse at 50% 10%, rgba(255,69,0,0.3) 0%, transparent 30%);
	pointer-events: none;
}

.login-text {
	color: #F5F5DC;
	font-size: 20px;
	font-weight: 900;
	letter-spacing: 3px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 1px 1px 2px rgba(139,0,0,0.6);
	position: relative;
	z-index: 10;
	transform: scale(1.05);
	filter: drop-shadow(0 0 3px rgba(245,245,220,0.3));
}

.forgot-password {
	align-self: center;
}

.forgot-text {
	color: #000;
	font-size: 14px;
	font-weight: 600;
}

.register-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 40px;
}

.register-text {
	color: #333;
	font-size: 14px;
	font-weight: 500;
}

.register-link {
	color: #8B0000;
	font-size: 14px;
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}
</style>
