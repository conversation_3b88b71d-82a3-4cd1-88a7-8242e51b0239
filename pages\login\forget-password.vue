<template>
	<view class="container">
		<view class="logo-container">
			<image src="http://*************:8890/i/2025/06/12/logo.png" class="logo" mode="aspectFit"></image>
		</view>
		
		<view class="form-container">
			<vol-form :formFields="formData" :formOptions="formOptions" :labelWidth="0" padding="0">
			</vol-form>
			
			<view class="verification-group">
				<view class="verification-input">
					<vol-form :formFields="codeData" :formOptions="codeOptions" :labelWidth="0" padding="0">
					</vol-form>
				</view>
				<view class="get-code-btn" @click="getVerificationCode">
					<text class="get-code-text">获取验证码</text>
				</view>
			</view>
			
			<view class="reset-btn-container">
				<view class="reset-btn" @click="resetPassword">
					<text class="reset-text">修改密码</text>
				</view>
			</view>
		</view>
		
		<view class="back-container">
			<text class="back-text" @click="goBack">返回登录</text>
		</view>
	</view>
</template>

<script>
	// 添加平台检测
	const getPlatform = () => {
		let platform = '';
		// #ifdef APP-PLUS
		platform = 'APP-PLUS';
		// #endif
		// #ifdef APP-PLUS-NVUE
		platform = 'APP-NVUE';
		// #endif
		// #ifdef H5
		platform = 'H5';
		// #endif
		// #ifdef MP-WEIXIN
		platform = 'MP-WEIXIN';
		// #endif
		// #ifdef MP-ALIPAY
		platform = 'MP-ALIPAY';
		// #endif
		// #ifdef MP-BAIDU
		platform = 'MP-BAIDU';
		// #endif
		// #ifdef MP-TOUTIAO
		platform = 'MP-TOUTIAO';
		// #endif
		// #ifdef MP-QQ
		platform = 'MP-QQ';
		// #endif
		console.log('页面运行平台:', platform);
		return platform;
	};
	
	export default {
		data() {
			return {
				formData: {
					phone: '',
					newPassword: '',
					confirmPassword: ''
				},
				codeData: {
					verificationCode: ''
				},
				formOptions: [
					{
						field: 'phone',
						title: '手机号',
						type: 'text',
						placeholder: '请输入手机号'
					},
					{
						field: 'newPassword',
						title: '新密码',
						type: 'password',
						placeholder: '请输入新密码'
					},
					{
						field: 'confirmPassword',
						title: '确认密码',
						type: 'password',
						placeholder: '请确认新密码'
					}
				],
				codeOptions: [
					{
						field: 'verificationCode',
						title: '验证码',
						type: 'text',
						placeholder: '请输入验证码'
					}
				],
				currentPlatform: getPlatform()
			}
		},
		created() {
			// 添加平台兼容性处理
			if (this.currentPlatform !== 'MP-WEIXIN' && typeof wx === 'undefined') {
				console.log('当前平台不支持wx对象，将使用uni API替代');
			}
		},
		methods: {
			getVerificationCode() {
				// 获取验证码逻辑
				console.log('获取验证码', this.formData.phone);
				if (!this.formData.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}
				// 这里添加发送验证码的API调用
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				});
			},
			resetPassword() {
				// 重置密码逻辑
				console.log('重置密码', this.formData.phone, this.formData.newPassword, this.formData.confirmPassword, this.codeData.verificationCode);
				if (!this.formData.phone || !this.formData.newPassword || !this.formData.confirmPassword || !this.codeData.verificationCode) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					});
					return;
				}
				if (this.formData.newPassword !== this.formData.confirmPassword) {
					uni.showToast({
						title: '两次密码输入不一致',
						icon: 'none'
					});
					return;
				}
				// 这里添加重置密码的API调用
				uni.showToast({
					title: '密码修改成功',
					icon: 'success'
				});
				// 延迟跳转到登录页面
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			},
			goBack() {
				// 返回登录页面
				uni.navigateBack();
			}
		}
	}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	background-image: url('http://*************:8890/i/2025/06/12/layout_ornament.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}

.logo-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 60px;
	margin-bottom: 40px;
}

.logo {
	width: 100px;
	height: 100px;
}

.form-container {
	flex: 1;
	width: 100%;
	max-width: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.title-container {
	margin-bottom: 30px;
}

.title-text {
	color: #2C1810;
	font-size: 28px;
	font-weight: 900;
	letter-spacing: 4px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 1px 1px 2px rgba(139,69,19,0.4);
	text-align: center;
}

.verification-group {
	width: 100%;
	margin-bottom: 20px;
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: 10px;
}

.verification-input {
	flex: 1;
}

.get-code-btn {
	width: 100px;
	height: 50px;
	background: linear-gradient(135deg, #8B4513, #CD853F, #DEB887);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: inset 0 2px 4px rgba(0,0,0,0.2), 0 2px 6px rgba(0,0,0,0.15);
	border: 1px solid rgba(139, 69, 19, 0.6);
	transition: all 0.3s ease;
}

.get-code-btn:active {
	transform: scale(0.95);
	box-shadow: inset 0 4px 8px rgba(0,0,0,0.3);
}

.get-code-text {
	color: #2C1810;
	font-size: 12px;
	font-weight: 700;
	letter-spacing: 1px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
}

.reset-btn-container {
	width: 100%;
	margin-top: 30px;
	margin-bottom: 20px;
}

.reset-btn {
	width: 100%;
	height: 50px;
	background: linear-gradient(135deg, #2C1810, #8B4513, #CD853F);
	border-radius: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	clip-path: polygon(5% 0%, 95% 0%, 98% 20%, 100% 50%, 98% 80%, 95% 100%, 5% 100%, 2% 80%, 0% 50%, 2% 20%);
	box-shadow: inset 0 2px 4px rgba(0,0,0,0.3), 0 4px 8px rgba(0,0,0,0.2);
	border: 1px solid rgba(139, 69, 19, 0.8);
}

.reset-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(205,133,63,0.4), transparent);
	transition: left 0.8s ease-in-out;
}

.reset-btn:active::before {
	left: 100%;
}

.reset-btn::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(ellipse at 20% 30%, rgba(139,69,19,0.6) 0%, transparent 50%),
	            radial-gradient(ellipse at 80% 70%, rgba(205,133,63,0.4) 0%, transparent 40%),
	            radial-gradient(ellipse at 50% 10%, rgba(222,184,135,0.3) 0%, transparent 30%);
	pointer-events: none;
}

.reset-text {
	color: #F5F5DC;
	font-size: 18px;
	font-weight: 900;
	letter-spacing: 3px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 1px 1px 2px rgba(139,69,19,0.6);
	position: relative;
	z-index: 10;
	transform: scale(1.05);
	filter: drop-shadow(0 0 3px rgba(245,245,220,0.3));
}

.back-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 40px;
}

.back-text {
	color: #8B4513;
	font-size: 16px;
	font-weight: bold;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
	letter-spacing: 2px;
}
</style>
