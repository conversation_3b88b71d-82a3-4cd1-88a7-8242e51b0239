<template>
	<view class="background-list-container">
		<!-- Tab导航栏 -->
		<view class="tab-section">
			<uv-tabs 
				:list="tabList" 
				:current="currentTab" 
				@change="onTabChange"
				lineColor="#8B1538"
				:activeStyle="{
					color: '#8B1538',
					fontWeight: 'bold'
				}"
			></uv-tabs>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-section">
			<view v-if="filteredBackgrounds.length === 0" class="empty-list">
				<text class="empty-text">暂无背景图片</text>
			</view>
			<view v-else class="background-grid">
				<view 
					class="background-item" 
					v-for="(item, index) in filteredBackgrounds" 
					:key="index"
					@tap="selectBackgroundImage(item)"
					:class="{'selectable': isSelectMode}"
				>
					<view class="background-preview">
						<image 
							:src="item.url" 
							mode="aspectFit" 
							class="background-image"
						></image>
					</view>
					<view class="background-info">
						<text class="background-name">{{ item.name }}</text>
						<view class="background-type">{{ item.type }}</view>
					</view>
					<view v-if="!isSelectMode" class="delete-icon" @tap.stop="confirmDelete(index)">
						<uv-icon name="trash" color="#8B1538" size="20"></uv-icon>
					</view>
					<view v-else class="select-icon">
						<uv-icon name="checkmark" color="#8B1538" size="20"></uv-icon>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加按钮 -->
		<view class="add-button" v-if="!isSelectMode" @tap="openAddPopup">
			<uv-icon name="plus" color="#FFFFFF" size="24"></uv-icon>
		</view>
		
		<!-- 添加背景弹窗 -->
		<uv-popup 
			ref="addPopup" 
			mode="center" 
			:closeOnClickOverlay="false"
			:safeAreaInsetBottom="true"
			:round="10"
			:closeable="true"
		>
			<view class="add-popup-content">
				<view class="popup-title">添加背景图片</view>
				
				<view class="form-item">
					<text class="form-label">图片名称</text>
					<input 
						class="form-input" 
						v-model="newBackground.name" 
						placeholder="请输入图片名称"
					/>
				</view>
				
				<view class="form-item">
					<text class="form-label">图片类型</text>
					<picker 
						@change="onTypeChange" 
						:value="typeIndex" 
						:range="backgroundTypes"
						class="form-picker"
					>
						<view class="picker-content">
							<text class="picker-text">{{ backgroundTypes[typeIndex] }}</text>
							<uv-icon name="arrow-down" size="14" color="#666"></uv-icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label">上传图片</text>
					<uv-upload
						ref="upload"
						:fileList="uploadFileList"
						@afterRead="afterRead"
						@delete="deleteUpload"
						:maxCount="1"
						:width="200"
						:height="200"
						uploadText="选择图片"
						imageMode="aspectFit"
					></uv-upload>
				</view>
				
				<view class="popup-actions">
					<view class="popup-btn cancel" @tap="closeAddPopup">取消</view>
					<view class="popup-btn confirm" @tap="submitForm">确认</view>
				</view>
			</view>
		</uv-popup>
		
		<!-- 删除确认弹窗 -->
		<uv-modal 
			ref="deleteModal" 
			title="删除确认" 
			content="确定要删除这张背景图片吗？" 
			:showCancelButton="true"
			@confirm="deleteBackground"
		></uv-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// Tab数据
				tabList: [
					{ name: '全部' },
					{ name: '管理局' },
					{ name: '大院' },
					{ name: '其他' }
				],
				currentTab: 0,
				
				// 背景图片列表
				backgrounds: [],
				
				// 背景类型
				backgroundTypes: ['管理局', '大院', '其他'],
				typeIndex: 0,
				
				// 新增背景数据
				newBackground: {
					name: '',
					type: '管理局',
					url: ''
				},
				
				// 上传相关
				uploadFileList: [],
				
				// 当前要删除的索引
				deleteIndex: -1,
				
				// 是否是选择模式
				isSelectMode: false
			}
		},
		computed: {
			// 根据当前选择的Tab过滤背景图片
			filteredBackgrounds() {
				if (this.currentTab === 0) {
					return this.backgrounds;
				} else {
					const tabType = this.tabList[this.currentTab].name;
					return this.backgrounds.filter(item => item.type === tabType);
				}
			}
		},
		onLoad(options) {
			// 检查是否是选择模式
			if (options && options.select === 'true') {
				this.isSelectMode = true;
			}
			
			// 模拟加载初始数据
			this.loadBackgrounds();
		},
		methods: {
			// 加载背景图片数据
			loadBackgrounds() {
				// 这里应该是从API获取数据，现在用模拟数据
				this.backgrounds = [
					{
						name: '管理局会议室背景',
						type: '管理局',
						url: 'http://www.chuantiba.com/api/storage/upload/1701705600/1701752546213847.png'
					},
					{
						name: '大院活动背景',
						type: '大院',
						url: 'http://www.chuantiba.com/api/storage/upload/1701878400/170193741739568.png'
					},
					{
						name: '其他类型背景',
						type: '其他',
						url: 'http://www.chuantiba.com/api/storage/upload/1701878400/1701926050311878.png'
					}
				];
			},
			
			// Tab切换事件
			onTabChange(e) {
				this.currentTab = e.index;
			},
			
			// 打开添加弹窗
			openAddPopup() {
				this.$refs.addPopup.open();
			},
			
			// 关闭添加弹窗
			closeAddPopup() {
				// 重置表单
				this.newBackground = {
					name: '',
					type: '管理局',
					url: ''
				};
				this.typeIndex = 0;
				this.uploadFileList = [];
				this.$refs.addPopup.close();
			},
			
			// 类型选择变更
			onTypeChange(e) {
				this.typeIndex = e.detail.value;
				this.newBackground.type = this.backgroundTypes[this.typeIndex];
			},
			
			// 上传后回调
			afterRead(event) {
				const file = event.file;
				// 这里应该调用上传API，现在只是更新本地预览
				const newFile = {};
				// 复制 file 对象的所有属性
				for (let key in file) {
					if (file.hasOwnProperty(key)) {
						newFile[key] = file[key];
					}
				}
				// 添加额外属性
				newFile.status = 'success';
				newFile.message = '上传成功';
				this.uploadFileList.push(newFile);
				
				this.newBackground.url = file.url;
			},
			
			// 删除上传文件
			deleteUpload() {
				this.uploadFileList = [];
				this.newBackground.url = '';
			},
			
			// 提交表单
			submitForm() {
				// 表单验证
				if (!this.newBackground.name.trim()) {
					uni.showToast({
						title: '请输入图片名称',
						icon: 'none'
					});
					return;
				}
				
				if (!this.newBackground.url) {
					uni.showToast({
						title: '请上传图片',
						icon: 'none'
					});
					return;
				}
				
				// 添加到列表
				const newItem = {};
				// 复制 newBackground 对象的所有属性
				for (let key in this.newBackground) {
					if (this.newBackground.hasOwnProperty(key)) {
						newItem[key] = this.newBackground[key];
					}
				}
				this.backgrounds.push(newItem);
				
				// 关闭弹窗
				this.closeAddPopup();
				
				// 提示成功
				uni.showToast({
					title: '添加成功',
					icon: 'success'
				});
			},
			
			// 确认删除弹窗
			confirmDelete(index) {
				this.deleteIndex = index;
				this.$refs.deleteModal.open();
			},
			
			// 删除背景图片
			deleteBackground() {
				if (this.deleteIndex !== -1) {
					const type = this.backgrounds[this.deleteIndex].type;
					
					// 从列表中删除
					this.backgrounds.splice(this.deleteIndex, 1);
					this.deleteIndex = -1;
					
					// 提示成功
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				}
			},
			
			// 选择背景图片并返回
			selectBackgroundImage(item) {
				if (this.isSelectMode) {
					// 显示加载中
					uni.showLoading({
						title: '正在处理图片...'
					});
					
					// 先下载网络图片到本地
					uni.downloadFile({
						url: item.url,
						success: (res) => {
							if (res.statusCode === 200) {
								console.log('图片下载成功，临时路径:', res.tempFilePath);
								
								// 将本地临时文件路径保存到存储
								uni.setStorageSync('selectedBackgroundImage', res.tempFilePath);
								
								// 隐藏加载提示
								uni.hideLoading();
								
								// 提示成功
								uni.showToast({
									title: '已选择背景',
									icon: 'success',
									duration: 1500
								});
								
								// 返回上一页
								setTimeout(() => {
									uni.navigateBack();
								}, 300);
							} else {
								throw new Error('下载状态码异常：' + res.statusCode);
							}
						},
						fail: (err) => {
							console.error('下载图片失败:', err);
							
							// 尝试直接使用URL
							try {
								uni.setStorageSync('selectedBackgroundImage', item.url);
								
								uni.hideLoading();
								uni.showToast({
									title: '图片可能无法正确显示',
									icon: 'none',
									duration: 2000
								});
								
								setTimeout(() => {
									uni.navigateBack();
								}, 300);
							} catch (e) {
								console.error('保存URL失败:', e);
								uni.hideLoading();
								uni.showToast({
									title: '选择背景失败',
									icon: 'none'
								});
							}
						}
					});
				}
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.background-list-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 导航栏样式 */
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #FFFFFF;
		border-bottom: 1px solid #f0f0f0;
	}
	
	.navbar-left {
		display: flex;
		align-items: center;
	}
	
	.navbar-title {
		font-size: 28rpx;
		color: #333;
		margin-left: 8rpx;
	}
	
	.navbar-center {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.navbar-right {
		width: 60rpx;
	}
	
	.tab-section {
		background-color: #FFFFFF;
	}
	
	.content-section {
		flex: 1;
		padding: 20rpx;
		overflow-y: auto;
	}
	
	.empty-list {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 300rpx;
	}
	
	.empty-text {
		color: #999;
		font-size: 28rpx;
	}
	
	.background-grid {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}
	
	.background-item {
		display: flex;
		align-items: center;
		background-color: #FFFFFF;
		border-radius: 10rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		position: relative;
	}
	
	.background-preview {
		width: 120rpx;
		height: 120rpx;
		border-radius: 8rpx;
		overflow: hidden;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f8f8f8;
	}
	
	.background-image {
		max-width: 100%;
		max-height: 100%;
		width: auto;
		height: auto;
	}
	
	.background-info {
		flex: 1;
	}
	
	.background-name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.background-type {
		font-size: 24rpx;
		color: #666;
		background-color: #f0f0f0;
		display: inline-block;
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
	}
	
	.delete-icon, .select-icon {
		padding: 16rpx;
	}
	
	.selectable {
		cursor: pointer;
		transition: all 0.2s;
	}
	
	.selectable:hover, .selectable:active {
		background-color: #f5f5f5;
		transform: translateY(-2rpx);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}
	
	.add-button {
		position: fixed;
		right: 40rpx;
		bottom: 40rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: #8B1538;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 16rpx rgba(139, 21, 56, 0.3);
		z-index: 10;
	}
	
	/* 弹窗样式 */
	.add-popup-content {
		width: 600rpx;
		padding: 30rpx;
	}
	
	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 30rpx;
		color: #333;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.form-label {
		display: block;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.form-input {
		width: 100%;
		height: 80rpx;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		padding: 0 20rpx;
		box-sizing: border-box;
		font-size: 28rpx;
	}
	
	.form-picker {
		width: 100%;
		height: 80rpx;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		font-size: 28rpx;
	}
	
	.picker-content {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
	}
	
	.popup-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 40rpx;
	}
	
	.popup-btn {
		width: 45%;
		height: 80rpx;
		border-radius: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}
	
	.cancel {
		background-color: #f5f5f5;
		color: #666;
	}
	
	.confirm {
		background-color: #8B1538;
		color: #fff;
	}
</style>
