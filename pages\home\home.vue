<template>
	<view class="container"> 
		<!-- 主要内容区域 -->
		<view class="content">
			<!-- 标题卡片 -->
			<view class="title-card">    
				<image class="banner-image" :src="bannerImage" mode="aspectFill" :lazy-load="true"></image>
				<view class="title-content">
					<text class="main-title">蓝牙投屏</text>
					<text class="sub-title">简单高效</text>
				</view>
				<view class="decoration"></view>
			</view>
			
			<!-- 功能按钮 -->
			<view class="button-group">
				<view class="function-button single-screen" @click="goToSingleScreen">
					<image class="button-bg" :src="buttonBgImage" mode="aspectFill" :lazy-load="true"></image>
					<text class="button-text">单一投屏</text>
				</view>
				
				<view class="function-button batch-screen" @click="goToBatchScreen">
					<image class="button-bg" :src="buttonBgImage" mode="aspectFill" :lazy-load="true"></image>
					<text class="button-text">批量投屏</text>
				</view> 
			</view>
		</view> 
		
		<!-- 背景装饰 -->
		<view class="background-decoration">
			<view class="mountain-bg"></view>
			<view class="cloud" style="right: 100rpx; bottom: 150rpx;"></view>
			<view class="cloud" style="right: 200rpx; bottom: 180rpx; width: 100rpx;"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bannerImage: '/static/images/bg.svg',
				buttonBgImage: '/static/images/btn.png'
			}
		},
		onLoad() {
			// 页面加载时的处理逻辑
			this.checkLoginStatus();
		},
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				const token = uni.getStorageSync('token');
				if (!token) {
					uni.navigateTo({
						url: '/pages/login/account-login'
					});
				}
			},
			
			// 导航到单一投屏页面
			goToSingleScreen() {
				uni.navigateTo({
					url: '/pages/subPackage/public/single-screen'
				});
			},
			
			// 导航到批量投屏页面
			goToBatchScreen() {
				uni.navigateTo({
					url: '/pages/subPackage/public/batch-screen'
				});
			} 
		}
	}
</script>


<style lang="scss"> 
.container {
	flex: 1; 
	position: relative;
	overflow: hidden; 
	min-height: 100vh;
}

.navbar {
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 40rpx;
	padding-top: calc(20rpx + var(--status-bar-height));
	height: 88rpx; 
}

.navbar-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
}

/* 小程序胶囊按钮样式 */
/* #ifdef MP */
.capsule-buttons {
	flex-direction: row;
	align-items: center;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 32rpx;
	padding: 8rpx;
	gap: 2rpx;
}

.capsule-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 24rpx;
	background: rgba(255, 255, 255, 0.3);
	justify-content: center;
	align-items: center;
}

.capsule-icon {
	font-size: 24rpx;
	color: #FFFFFF;
	font-weight: bold;
}
/* #endif */

.content {
	flex: 1;
	padding: 60rpx 40rpx;
	align-items: center;
}

.title-card {
	width: 670rpx;
	height: 320rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	margin-bottom: 80rpx;
	position: relative;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.title-content {
	position: absolute;
	left: 40rpx;
	top: 50%;
	transform: translateY(-50%);
	z-index: 2;
}

.main-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #2C3E50;
	margin-bottom: 16rpx;
	display: flex;
}

.sub-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #2C3E50;
	display: flex;
}

.decoration {
	position: absolute;
	right: 0;
	top: 0;
	width: 300rpx;
	height: 100%;
	background: linear-gradient(135deg, rgba(139, 21, 56, 0.1) 0%, rgba(160, 82, 45, 0.1) 100%);
}

.banner-image {
	position: absolute;
	right: 0;
	top: 0;
	width: 100%;
	height: 100%;
	opacity: 0.8;
}

.button-group {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.function-button {
	width: 500rpx;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	position: relative;
	background: transparent;
	transition: transform 0.2s;
}

.function-button:active {
	transform: scale(0.98);
}

.single-screen {
	/* 特定样式 */
}

.batch-screen {
	/* 特定样式 */
}

.device-manage {
	/* 特定样式 */
}

.user-center {
	/* 特定样式 */
}

.button-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
	border-radius: 50rpx;
}

.button-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
	text-align: center;
	position: relative;
	z-index: 2;
}

.background-decoration {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 400rpx;
}

.mountain-bg {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 200rpx;
	background: linear-gradient(to top, rgba(139, 139, 139, 0.2) 0%, transparent 100%);
	border-radius: 50% 50% 0 0;
}

.cloud {
	position: absolute;
	bottom: 150rpx;
	right: 100rpx;
	width: 80rpx;
	height: 40rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 20rpx;
	opacity: 0.6;
	animation: float 10s infinite linear;
}

@keyframes float {
	0% {
		transform: translateX(0);
	}
	50% {
		transform: translateX(-30rpx);
	}
	100% {
		transform: translateX(0);
	}
}
</style>
